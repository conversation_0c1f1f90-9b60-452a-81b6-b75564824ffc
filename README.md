# Wolanin MD Aesthetics - Booking Widget

A comprehensive booking system for medical aesthetics appointments, built with Next.js 15, TypeScript, and modern web technologies.

## Features

### 🎯 Core Booking System
- **Multi-step booking wizard** with intuitive user flow
- **Service selection** with category filtering and search
- **Calendar integration** with real-time slot availability
- **Patient information forms** with validation
- **Booking confirmation** and success pages

### 👥 User Management
- **Role-based access control** (Admin/User)
- **Better Auth integration** with email/password authentication
- **User dashboard** for appointment management
- **Admin dashboard** for system management

### 🔗 CliniCore Integration
- **Real-time synchronization** with CliniCore API
- **Patient management** with automatic creation/updates
- **Appointment booking** directly to CliniCore
- **Service and slot fetching** from external system

### 📱 Modern UI/UX
- **Mobile-responsive design** with Tailwind CSS
- **Radix UI components** for accessibility
- **Loading states and error handling**
- **Toast notifications** for user feedback

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth
- **State Management**: Redux Toolkit
- **UI Components**: Radix UI + Tailwind CSS
- **Forms**: React Hook Form + Zod validation
- **Deployment**: Cloudflare with OpenNext

## Getting Started

### Prerequisites

- Node.js 18+ and pnpm
- PostgreSQL database
- CliniCore API credentials

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd booking-widget
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

4. Set up the database:
```bash
pnpm db:generate
pnpm db:push
```

5. Run the development server:
```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Environment Variables

See `.env.example` for all required environment variables:

- `DATABASE_URL` - PostgreSQL connection string
- `BETTER_AUTH_SECRET` - Secret for Better Auth
- `CC_API_URL` - CliniCore API base URL
- `CC_API_TOKEN` - CliniCore API token
- `CC_PUBLIC_TOKEN` - CliniCore public token

## Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   │   └── booking/       # Booking-related endpoints
│   ├── my-appointments/   # User dashboard
│   ├── ~/                 # Admin dashboard
│   └── page.tsx          # Main booking page
├── components/            # React components
│   ├── booking/          # Booking-specific components
│   └── ui/               # Reusable UI components
├── database/             # Database schema and migrations
├── lib/                  # Utility libraries
│   └── clinicore.ts     # CliniCore API integration
├── store/                # Redux store and slices
├── types/                # TypeScript type definitions
└── utils/                # Helper utilities
```

## API Routes

### Booking System
- `GET /api/booking/services` - Fetch available services
- `GET /api/booking/slots` - Get available time slots
- `POST /api/booking/book` - Create new appointment
- `GET /api/booking/appointments` - Fetch user appointments

### Authentication
- Better Auth handles authentication endpoints automatically

## User Flows

### 1. Main Booking Flow (/)
1. **Service Selection** - Browse and select treatments
2. **Calendar Selection** - Choose date and time
3. **Patient Information** - Enter contact details
4. **Confirmation** - Review and confirm booking
5. **Success** - Booking confirmation

### 2. User Dashboard (/my-appointments)
- View upcoming and past appointments
- Cancel appointments (with 24h notice)
- Update patient information
- Download confirmations

### 3. Admin Dashboard (/~)
- View appointment statistics
- Manage appointments and patients
- Configure services and settings
- Access system reports

## Database Schema

### Core Tables
- `users` - User accounts with roles
- `patients` - Patient information (synced with CliniCore)
- `appointments` - Appointment records
- `services` - Available treatments
- `product_groups` - Service categories
- `time_slots` - Available appointment slots

### Authentication Tables
- `sessions` - User sessions
- `accounts` - OAuth accounts
- `verifications` - Email/phone verification

## Development

### Database Operations
```bash
# Generate migrations
pnpm db:generate

# Apply migrations
pnpm db:push

# Sync both
pnpm db:sync
```

### Type Checking
```bash
# Run TypeScript compiler
pnpm type-check
```

### Linting
```bash
# Run ESLint
pnpm lint
```

## Deployment

The application is configured for deployment on Cloudflare using OpenNext:

```bash
# Build for production
pnpm build

# Deploy to Cloudflare
pnpm deploy

# Preview deployment
pnpm preview
```

## Contributing

1. Follow the existing code style and patterns
2. Use TypeScript for all new code
3. Add proper error handling and loading states
4. Test thoroughly before submitting PRs
5. Update documentation as needed

## License

This project is proprietary software for Wolanin MD Aesthetics.

## Support

For technical support or questions, contact the development team.
