declare global {
    interface IProductGroup {
        id: number;
        parent: number | null;
        children: unknown[];
        name: string;
        accountingNumber: unknown | null;
        order: number;
        products: unknown[];
        services: number[];
    }

    interface IPublicService {
        id: number;
        name: string;
        duration: number;
        groupNames: string[];
        price: string | number;
        description: string;
        users: string[];
        bookingRequiresPayment: boolean;
    }

    interface IServices {
        id: number;
        name: string;
        externalName: string;
        description: string;
        additionalText: string;
        gross: number;
        taxRate: number;
        duration: number;
        ownCosts: number;
        ownCostsPercent: number | null;
        barcode: string;
        accountingNumber: number | null;
        costCenter: number | null;
        productGroup: number;
        packages: unknown[];
        resources: unknown[];
        invoiceProducts: unknown[];
        historyProducts: unknown[];
        products: unknown[];
        reminderActive: boolean;
        notificationsActive: boolean;
        automaticallyAddHistoryProducts: boolean;
        bookingRequiresPayment: boolean;
        bookingHoursInAdvance: number | null;
        bookingSelfServiceInAdvance: number | null;
        deletedAt: string | null;
        onlineGroup: string;
    }

    /**
     * Represents a service in the store, including display and booking details.
     */
    interface IStoreService {
        id: string;
        ccID: number;
        groupId: string | null;
        name: string;
        duration: number;
        price: number;
        /** Optional description of the service */
        description?: string | null;
        /** Display name for the service, if different from name */
        displayName: string | null;
        /** Display description for the service */
        displayDescription: string | null;
        /** Order for displaying the service */
        displayOrder: number;
        /** Image URL or path for the service */
        image: string | null;
        /** List of user IDs associated with the service */
        users: string[] | null;
        /** Whether the service is hidden from the booking widget */
        hidden: boolean;
        createdAt?: Date;
        updatedAt?: Date;
    }

    /**
     * Represents a product group with its associated services for the dashboard.
     */
    interface IProductGroupWithServices {
        id: string;
        ccID: number;
        name: string;
        order: number;
        /** Display name for the group, if different from name */
        displayName: string | null;
        /** Order for displaying the group */
        displayOrder: number;
        /** Whether the group is hidden from the booking widget */
        hidden: boolean;
        createdAt?: Date;
        updatedAt?: Date;
        /** List of services belonging to this group */
        services: IStoreService[];
    }
}

export { }