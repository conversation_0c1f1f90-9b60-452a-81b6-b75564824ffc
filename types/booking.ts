import { z } from 'zod';

// Base types from the old system, modernized
export interface Service {
  id: number | string;
  ccID: number;
  name: string;
  displayName?: string;
  duration: number; // in seconds
  price: number; // in cents
  description?: string;
  displayDescription?: string;
  groupId?: string;
  image?: string;
  hidden: boolean;
  users?: string[];
  displayOrder: number;
  // Legacy compatibility
  externalName?: string;
  gross?: number;
  ac_active_value?: number;
}

export interface Category {
  id: number | string;
  ccID?: number;
  name: string;
  displayName?: string;
  services: Service[];
  order: number;
  displayOrder: number;
  hidden: boolean;
}

export interface Appointment {
  id: string;
  ccAppointmentId?: number;
  patientId: string;
  userId?: string;
  serviceIds: number[];
  slotDate: Date;
  duration: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show';
  notes?: string;
  location?: string;
  resources?: number[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TimeSlot {
  id: string;
  ccSlotId?: string;
  serviceId: number;
  date: Date;
  available: boolean;
  duration: number;
  location?: string;
  resources?: number[];
  // Legacy compatibility
  service?: number | string;
}

export interface Patient {
  id: string;
  ccID: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BookingSession {
  id: string;
  sessionToken: string;
  selectedServiceIds?: number[];
  selectedSlotId?: string;
  patientData?: PatientFormData;
  status: 'draft' | 'pending' | 'confirmed' | 'cancelled';
  expiresAt: Date;
}

// Form data types
export interface PatientFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message?: string;
  birthday?: string;
  tos?: boolean;
}

// Booking flow state
export interface BookingState {
  currentStep: 'services' | 'calendar' | 'form' | 'confirmation' | 'success';
  selectedServices: Service[];
  selectedSlot: TimeSlot | null;
  patientData: PatientFormData;
  totalDuration: number;
  totalPrice: number;
  isLoading: boolean;
  error: string | null;
}

// API response types
export interface ServicesResponse {
  status: number;
  message: string;
  data: Category[];
}

export interface SlotsResponse {
  status: number;
  message: string;
  data: Record<string, TimeSlot[]>; // grouped by date
}

export interface BookingResponse {
  status: number;
  message: string;
  appointment?: Appointment;
}

export interface AppointmentsResponse {
  appointments: Appointment[];
  patient: Patient;
}

// Validation schemas using Zod
export const patientFormSchema = z.object({
  firstName: z.string().min(1, 'Vorname ist erforderlich'),
  lastName: z.string().min(1, 'Nachname ist erforderlich'),
  email: z.string().email('Ungültige E-Mail-Adresse'),
  phone: z.string().min(1, 'Telefonnummer ist erforderlich'),
  message: z.string().optional(),
  birthday: z.string().optional(),
  tos: z.boolean().refine((val: boolean) => val === true, {
    message: 'Sie müssen den Nutzungsbedingungen zustimmen'
  }).optional(),
});

export const serviceSelectionSchema = z.object({
  serviceIds: z.array(z.number()).min(1, 'Mindestens ein Service muss ausgewählt werden'),
});

export const slotSelectionSchema = z.object({
  slotId: z.string().min(1, 'Ein Termin muss ausgewählt werden'),
  date: z.date(),
});

export const bookingConfirmationSchema = z.object({
  serviceIds: z.array(z.number()),
  slotId: z.string(),
  patientData: patientFormSchema,
});

// CliniCore API types (from old system)
export interface CCService {
  id: string;
  name: string;
  duration: number;
  price: string;
  groupNames?: string[];
  productGroup?: string;
}

export interface CCProductGroup {
  id: string;
  name: string;
  services?: CCService[];
}

export interface CCSlot {
  id: string;
  date: string;
  service: number | string;
  location?: string;
  [key: string]: any;
}

export interface CCPatient {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneMobile: string;
  appointments: number[];
}

export interface CCAppointment {
  id: number;
  startsAt: string;
  endsAt: string;
  patients: number[];
  people: number[];
  services: number[];
  description?: string;
}

// User role types
export type UserRole = 'admin' | 'user';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  emailVerified: boolean;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Navigation and UI types
export interface BookingStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  current: boolean;
}

export interface ServiceFilter {
  categoryId?: string;
  searchTerm?: string;
  priceRange?: [number, number];
  durationRange?: [number, number];
}

// Error types
export interface BookingError {
  code: string;
  message: string;
  details?: any;
}

// Settings types
export interface BookingSettings {
  timezone: string;
  advanceHours: number;
  windowDays: number;
  emailNotifications: boolean;
  smsNotifications: boolean;
  allowCancellation: boolean;
  cancellationHours: number;
}

export type BookingFormData = z.infer<typeof patientFormSchema>;
export type ServiceSelection = z.infer<typeof serviceSelectionSchema>;
export type SlotSelection = z.infer<typeof slotSelectionSchema>;
export type BookingConfirmation = z.infer<typeof bookingConfirmationSchema>;
