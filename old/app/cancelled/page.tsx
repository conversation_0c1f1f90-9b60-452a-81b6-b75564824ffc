import Link from 'next/link';
import { FC } from 'react';

interface pageProps {}

const page: FC<pageProps> = ({}) => {
  return (
    <div className="relative">
      <div className="booking-widget">
        <div className="container">
          <div className="appointments overflow-y-auto h-[96vh] lg:h-[62vh] scrollbar pb-4">
            <h3 className="font-semibold text-xl text-center">
              Ihr Termin wurde storniert.
            </h3>
            <div className="text-center mt-12 flex gap-4 items-center justify-center">
              <Link href="/" className="font-medium action continue">
                Einen neuen Termin buchen
              </Link>{" "}
              <Link href="/my-appointments" className="action continue">
                Meine Termine
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
