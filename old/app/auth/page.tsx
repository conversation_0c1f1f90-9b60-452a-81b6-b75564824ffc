'use client';
import { ChangeEvent, FC, FormEvent, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { cn } from '../lib/cn';
import request from '@/app/api/request';
import Preloader from '../components/atoms/Preloader';
import OTP from '../components/OTP';
import { useRouter } from 'next/navigation';

interface pageProps {}
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const Page: FC<pageProps> = ({}) => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [isSentOTP, setIsSentOTP] = useState(false);
  const router = useRouter();

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    setEmail(event.target.value);
    ``;
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Email validation
    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }
    setError('');
    setLoading(true);
    const payload: any = {
      to: email,
    };

    try {
      await request('/send-email', {
        method: 'POST',
        body: payload,
      });

      setIsSentOTP(true);
    } catch (error) {
      toast.error(
        'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    request('/isLoggedIn', { method: 'GET' })
      .then((r) => {
        router.push(`/my-appointments`);
      })
      .catch((error) => {});
  }, []);

  return (
    <div className="h-auto border p-4 my-4 relative overflow-hidden rounded-md 2xl:w-[30%] lg:w-[65%] lg:h-[65%] md:p-4 mx-4 lg:mx-auto">
      {!isSentOTP ? (
        <form
          onSubmit={handleSubmit}
          className="flex justify-center items-center flex-col"
        >
          <input
            type="text"
            id="email"
            className={cn(
              "w-full  border border-[#727272] rounded-md p-2 focus:ring-0 placeholder:text-sm",
              error ? "border-red" : ""
            )}
            placeholder="E-Mail"
            value={email}
            onChange={handleChange}
          />
          <p className="mt-1 mb-2 text-red text-xs">{error}</p>
          <button className="action continue">OTP senden</button>
        </form>
      ) : (
        <OTP email={email} />
      )}
      <Preloader isLoading={loading} />
    </div>
  );
};

export default Page;
