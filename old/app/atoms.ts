import { atom } from 'jotai';

const isLoading = atom(true);
const allServicesState = atom<Category[]>([]);
const filteredDataState = atom<Category[]>([]);
const servicesState = atom([]);
const selectedServiceState = atom<Service[]>([]);
const activeSlotState = atom<null | Slot>(null);
const currentPageState = atom('services');
const activeNumberState = atom<number>(1);
const totalDurationState = atom<number>(0);
const activeCategoryState = atom<'' | string | number>('');
const formDataState = atom({
  firstName: '',
  lastName: '',
  phone: '',
  email: '',
  id: '',
});
const appointmentServicesState = atom<number[]>([]);
const gotoManageAppointmentsState = atom<boolean>(false);
const appointmentState = atom<any>(null);
const successTextState = atom<string>('');
const isRescheduledState = atom<boolean>(false);

export {
  allServicesState,
  isLoading,
  filteredDataState,
  servicesState,
  selectedServiceState,
  currentPageState,
  activeNumberState,
  activeSlotState,
  totalDurationState,
  activeCategoryState,
  formDataState,
  appointmentServicesState,
  gotoManageAppointmentsState,
  appointmentState,
  successTextState,
  isRescheduledState,
};
