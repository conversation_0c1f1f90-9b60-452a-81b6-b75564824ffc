import { useAtom } from 'jotai';
import { FC } from 'react';
import {
  activeNumberState,
  currentPageState,
  selectedServiceState,
} from '../atoms';
import Service from '../components/Service';
import useServiceSelection from '@/app/hooks/useServiceSelection';
import Header from '../components/atoms/Header';

interface SelectedServicesProps {}

const SelectedServices: FC<SelectedServicesProps> = ({}) => {
  const [selectedServices, setSelectedServices] = useAtom(selectedServiceState);
  const { handleSelectService } = useServiceSelection();
  const [, setCurrentPage] = useAtom(currentPageState);
  const [, setActiveNumber] = useAtom(activeNumberState);

  const handleBackClick = () => {
    setCurrentPage('services');
    setSelectedServices([]);
    setActiveNumber(1);
  };

  const Continue = () => {
    setCurrentPage('continue');
  };

  const addMoreServices = () => {
    setCurrentPage('addMoreServices');
  };

  return (
    <>
      <Header title="Ausgewählte Dienstleistungen" />
      <div className="w-full mt-6 h-full flex flex-col justify-between pb-12">
        <div className="selected-services h-[90%] overflow-y-auto scrollbar pb-4">
          <div className="grid grid-cols-1 gap-4">
            {selectedServices.length > 0
              ? selectedServices.map((service: Service) => (
                  <>
                    <Service
                      handleSelectService={handleSelectService}
                      service={service}
                      key={service.id}
                      activeNumber={selectedServices.find(
                        (num) => num.id === service.id,
                      )}
                    />
                  </>
                ))
              : ''}
          </div>
        </div>
        <div className="flex justify-between py-4 border-t-2 gap-3 text-sm lg:text-base">
          <button className="action" onClick={handleBackClick}>
            Zurück
          </button>
          <div className="flex gap-4">
            <button onClick={addMoreServices} className="action">
              Dienste hinzufügen
            </button>
            <button className="continue action" onClick={Continue}>
              Weiter
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default SelectedServices;
