'use client';

import { FC, useEffect } from 'react';
import { useAtom } from 'jotai';
import toast from 'react-hot-toast';
import {
  allServicesState,
  currentPageState,
  filteredDataState,
  isLoading,
  selectedServiceState,
} from '@/app/atoms';
import request from '@/app/api/request';
import Category from '../components/Category';
import Search from '../components/Search';
import ServicesContent from '../components/ServicesContent';
import Link from 'next/link';

interface ServicesProps {
  isLoggedIn: boolean;
}

const Services: FC<ServicesProps> = ({ isLoggedIn }) => {
  // Global State
  const [, setLoading] = useAtom(isLoading);
  const [allServices, setAllServices] = useAtom(allServicesState);
  const [, setFilteredData] = useAtom(filteredDataState);
  const [, setCurrentPage] = useAtom(currentPageState);
  const [selectedServices] = useAtom(selectedServiceState);

  useEffect(() => {
    if (!allServices.length) {
      setLoading(true);
      request('/services', { method: 'GET' })
        .then((response) => {
          if (response.data?.length) {
            const data: Category[] = response.data
              .map((category: Category) => {
                if (category.services?.length) {
                  return category;
                } else {
                  return null;
                }
              })
              .filter((element: any) => element !== null);
            const allServices: Service[] = [];
            data.forEach((category: Category) => {
              allServices.push(...category.services);
            });

            const allItems = {
              id: 0,
              name: 'Alle',
              services: allServices,
            };

            data.unshift(allItems);
            setAllServices(data);
            setFilteredData(data);
          }
        })
        .catch((error) => {
          toast.error(
            'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später noch einmal.',
          );
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, []);

  const handleNext = () => {
    if (!selectedServices.length) return;
    setCurrentPage('continue');
  };

  return (
    <div className="services">
      <div className="content">
        <div className="flex justify-between">
          <Search />
          <div className="gap-2 hidden md:flex">
            {isLoggedIn && (
              <Link
                href="/my-appointments"
                className="action continue !text-xs !flex justify-center items-center"
              >
                My appointments
              </Link>
            )}
            <button
              className="action continue !text-xs"
              onClick={handleNext}
              disabled={selectedServices.length ? false : true}
            >
              Weiter
            </button>
          </div>
        </div>
        <div className="main-content">
          <Category />
          <div className="flex justify-center items-center md:hidden">
            <div className="w-fit">
              {isLoggedIn && (
                <Link href="/my-appointments" className="action continue mr-2">
                  My appointments
                </Link>
              )}
              <button
                className="action continue mt-2 mx-auto"
                onClick={handleNext}
                disabled={selectedServices.length ? false : true}
              >
                Weiter
              </button>
            </div>
          </div>

          <ServicesContent />
        </div>
      </div>
    </div>
  );
};

export default Services;
