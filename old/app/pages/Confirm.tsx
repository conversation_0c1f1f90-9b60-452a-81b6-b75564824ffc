import { FC, useEffect, useState } from 'react';
import { useAtom } from 'jotai';
import Header from '../components/atoms/Header';
import {
  activeNumberState,
  currentPageState,
  selectedServiceState,
} from '../atoms';
import Icon from '../components/atoms/Icon';
import convertMinutes from '../lib/convertMinutes';
import truncateText from '../lib/truncateText';
import convertToDecimal from '../lib/convertToDecimal';
import Image from 'next/image';
import removeHtmlTags from '../lib/removeHtmlTags';

interface ConfirmProps {}

const Confirm: FC<ConfirmProps> = ({}) => {
  // Global State
  const [, setCurrentPage] = useAtom(currentPageState);
  const [selectedServices, setSelectedServices] = useAtom(selectedServiceState);
  const [currentNumber, setCurrentNumber] = useAtom(activeNumberState);

  // Local State
  const [totalDuration, setTotalDuration] = useState<number>(0);
  const [totalPrice, setTotalPrice] = useState<string>('');

  const handleBackClick = () => {
    setCurrentPage('services');
  };
  const addMoreServices = () => {
    setCurrentPage('addMoreServices');
  };
  const confirm = () => {
    setCurrentPage('calendar');
  };

  const removeService = (id: string | number) => {
    const updatedServices = selectedServices
      .filter((service: Service) => service.id !== id)
      .map((service: Service) => {
        return {
          ...service,
          ac_active_value:
            service.ac_active_value > 1
              ? service.ac_active_value - 1
              : service.ac_active_value,
        };
      });
    setSelectedServices(updatedServices);
    setCurrentNumber(currentNumber - 1);
    if (!updatedServices.length) {
      setCurrentPage('services');
    }
  };

  useEffect(() => {
    const totalDuration = selectedServices.reduce(
      (acc, curr) => acc + curr.duration,
      0,
    );
    const totalPrice = selectedServices.reduce(
      (acc, curr) => acc + (curr?.gross ? curr?.gross : 0),
      0,
    );
    setTotalDuration(totalDuration);
    setTotalPrice(convertToDecimal(totalPrice));
  }, [selectedServices]);

  return (
    <div className="confirm-services">
      <Header title="Ausgewählte Dienstleistungen" />
      <div className="overflow-y-auto scrollbar max-h-[84vh] h-auto md:h-[82%]">
        <div className="flex flex-col gap-4 my-4">
          {selectedServices.map((service, index: number) => (
            <div key={service.id} className="item">
              <div className="content">
                <div className="img">
                  <Image
                    src={`/images/${service.id}.png`}
                    alt=""
                    width={200}
                    height={200}
                  />
                </div>
                <div className="details">
                  <div>
                    <h3 className="font-semibold truncate-2-lines" title={service.externalName}>
                      {service.externalName}
                    </h3>
                    <p
                      className="text-sm truncate-2-lines"
                      title={removeHtmlTags(service.description || '')}
                    >
                      {removeHtmlTags(service.description || '')}
                    </p>
                  </div>
                  <div className="meta">
                    <span className="data">
                      <Icon icon="mingcute:time-line" className="w-5 h-5" />
                      <span>{service.duration}</span>
                      mins
                    </span>
                    <span className="data">
                      ab €<span>{convertToDecimal(service?.gross || 0)}</span>
                    </span>
                  </div>
                </div>
              </div>
              <button onClick={() => removeService(service.id)}>
                <Icon icon="uil:times" className="w-6" />
              </button>
            </div>
          ))}
        </div>
        <div className="total">
          <div className="duration">
            <span>Dauer</span>
            <b>{convertMinutes(totalDuration)}</b>
          </div>
          <div className="total">
            <span>Gesamt</span>
            <b>ab € {totalPrice}</b>
          </div>
        </div>
        <div className="flex lg:hidden justify-between mt-4 gap-1">
          <button className="action" onClick={handleBackClick}>
            Zurück
          </button>
          <div className="flex gap-4">
            {/* <button onClick={addMoreServices} className="action">
              Dienste hinzufügen
            </button> */}
            <button className="action continue" onClick={confirm}>
              Bestätigen
            </button>
          </div>
        </div>
      </div>

      <div className="footer">
        <button className="action" onClick={handleBackClick}>
          Zurück
        </button>
        <div className="flex gap-4">
          {/* <button onClick={addMoreServices} className="action">
            Dienste hinzufügen
          </button> */}
          <button className="action continue" onClick={confirm}>
            Bestätigen
          </button>
        </div>
      </div>
    </div>
  );
};

export default Confirm;
