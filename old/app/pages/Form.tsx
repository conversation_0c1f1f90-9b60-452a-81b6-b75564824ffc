import { ChangeEvent, FC, useEffect, useState } from 'react';
import PhoneInput from 'react-phone-input-2';
import { useAtom } from 'jotai';
import de from 'react-phone-input-2/lang/de.json';
import toast from 'react-hot-toast';

import Header from '../components/atoms/Header';
import 'react-phone-input-2/lib/style.css';
import request from '@/app/api/request';
import {
  activeNumberState,
  activeSlotState,
  appointmentState,
  currentPageState,
  formDataState,
  isLoading,
  isRescheduledState,
  selectedServiceState,
  successTextState,
} from '../atoms';

interface FormProps {}

const initialData = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  message: '',
};

const Form: FC<FormProps> = ({}) => {
  // Global State
  const [selectedServices, setSelectedServices] = useAtom(selectedServiceState);
  const [activeSlot, setActiveSlot] = useAtom(activeSlotState);
  const [, setLoading] = useAtom(isLoading);
  const [, setCurrentPage] = useAtom(currentPageState);
  const [, setActiveNumber] = useAtom(activeNumberState);
  const [formData] = useAtom(formDataState);
  const [appointment] = useAtom(appointmentState);
  const [, setSuccessText] = useAtom(successTextState);
  const [isRescheduled] = useAtom(isRescheduledState);

  // Local State
  const [location, setLocation] = useState<string>();
  const [totalDuration, setTotalDuration] = useState<number>(0);
  const [serviceIds, setServiceIds] = useState<number | string[]>([]);

  const [form, setForm] = useState<any>(initialData);

  const handleInputChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = event.target;
    setForm((prevFormData: any) => ({
      ...prevFormData,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    if (
      !form?.firstName.trim() ||
      !form?.lastName.trim() ||
      !form?.email.trim() ||
      !form?.phone.trim()
    ) {
      toast.error('Bitte füllen Sie die erforderlichen Felder aus.');
      return;
    }
    setLoading(true);
    // @ts-ignore
    const data = {
      ...form,
      patient: formData.id,
      duration: totalDuration,
      services: serviceIds,
      slot: activeSlot?.date,
    };

    try {
      if (isRescheduled) {
        try {
          await request(`/update-appointments/${appointment.id}`, {
            method: 'PUT',
            // @ts-ignore
            body: data,
          });
          setSuccessText('Appointments updated successfully');
        } catch (error) {}
      } else {
        await request('/book', { body: data, method: 'POST' });
      }

      setForm(initialData);
      setActiveNumber(1);
      setCurrentPage('success');
    } catch {
      toast.error(
        'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBackClick = () => {
    setCurrentPage('calendar');
    setActiveSlot(null);
  };

  useEffect(() => {
    fetch(
      'https://services.leadconnectorhq.com/funnels/funnel/geo-location/',
    ).then(async (res) => {
      const data = await res.json();
      if (!formData.phone) {
        setLocation(data.country);
      }
    });
  }, []);

  useEffect(() => {
    const data = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      phone: formData.phone,
    };
    setForm((prev: any) => ({ ...prev, ...data }));
  }, [formData]);

  useEffect(() => {
    const totalDuration = selectedServices.reduce(
      (acc, curr) => acc + curr.duration,
      0,
    );
    // @ts-ignore
    setServiceIds(selectedServices.map((service: Service) => service.id));
    setTotalDuration(totalDuration);
  }, [selectedServices]);

  return (
    <div className="form flex flex-col justify-between h-full p-3">
      <div className="h-[90%]">
        <Header title="Geben Sie Ihre Daten ein" />
        <div className="main-content mt-4 max-h-[84vh] h-auto md:h-full overflow-y-auto">
          <form className="overflow-y-auto scrollbar h-auto max-h-[78%] md:h-[90%]">
            <div className="form grid grid-cols-1 gap-3 md:grid-cols-2">
              <div>
                <label className="block mb-2">Vorname *</label>
                <input
                  type="text"
                  name="firstName"
                  placeholder="Vorname"
                  required
                  className="w-full h-12 focus:ring-0 focus:outline-none border border-[#727272] rounded-md p-2"
                  onChange={handleInputChange}
                  value={form.firstName}
                />
              </div>

              <div>
                <label className="block mb-2">Nachname *</label>
                <input
                  type="text"
                  name="lastName"
                  placeholder="Nachname"
                  required
                  className="w-full h-12 focus:ring-0 focus:outline-none border border-[#727272] rounded-md p-2"
                  onChange={handleInputChange}
                  value={form.lastName}
                />
              </div>

              <div>
                <label className="block mb-2">Telefon *</label>
                <PhoneInput
                  country={location?.toLowerCase()}
                  value={form.phone}
                  containerClass={`!border !border-[#727272] rounded-md`}
                  inputClass="!h-[47px] !border-none !w-full"
                  buttonClass="!border-none !rounded-l-md"
                  localization={de}
                  placeholder="+49 1xxx xxxxxx"
                  onChange={(phone) =>
                    setForm((prevFormData: any) => ({
                      ...prevFormData,
                      phone: phone,
                    }))
                  }
                />
              </div>

              <div>
                <label className="block mb-2">E-Mail *</label>
                <input
                  type="email"
                  name="email"
                  placeholder="EMail"
                  required
                  className="w-full h-12 focus:ring-0 focus:outline-none border border-[#727272] rounded-md p-2 mb-3"
                  onChange={handleInputChange}
                  value={form.email}
                />
              </div>
            </div>
            <label className="block mb-2">Weitere Informationen</label>
            <textarea
              name="message"
              id=""
              className="w-full h-[6.2rem] resize-none focus:outline-none border border-[#727272] rounded-md p-2 mb-4 focus:ring-0"
              placeholder="Weitere Informationen"
              title="message"
              onChange={handleInputChange}
              value={form.message}
            />
          </form>
          <div className="flex lg:hidden justify-between mx-4  mb-6">
            <button className="action" onClick={handleBackClick}>
              Zurück
            </button>
            <button
              onClick={handleSubmit}
              className={`continue action ${activeSlot ? 'active' : ''}`}
              disabled={activeSlot ? false : true}
            >
              Termin vereinbaren
            </button>
          </div>
        </div>
      </div>
      <div className="hidden lg:flex justify-between border-t-2 pt-2 -mb-3 lg:mb-0 bg-white">
        <button className="action" onClick={handleBackClick}>
          Zurück
        </button>
        <button
          onClick={handleSubmit}
          className={`continue action ${activeSlot ? 'active' : ''}`}
          disabled={activeSlot ? false : true}
        >
          Termin vereinbaren
        </button>
      </div>
    </div>
  );
};

export default Form;
