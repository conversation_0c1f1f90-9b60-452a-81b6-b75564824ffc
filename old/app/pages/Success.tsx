'use client';

import { FC, useState } from 'react';
// import { AiFillClockCircle, AiTwotoneCalendar } from 'react-icons/ai';
// import { MdLocationOn, MdMap } from 'react-icons/md';
import { useEffect } from 'react';
import moment from 'moment';
import 'moment/locale/de';
import {
  activeSlotState,
  selectedServiceState,
  successTextState,
  totalDurationState,
} from '../atoms';
import { useAtom } from 'jotai';
import convertMinutes from '../lib/convertMinutes';

interface SuccessProps {}

interface ServiceInfo {
  startDate: string;
  serviceName: string;
  startTime: string;
  endTime: string;
  location: string;
  duration: string | number;
}

const Success: FC<SuccessProps> = ({}) => {
  // Global State
  const [selectedSlot] = useAtom(activeSlotState);
  const [duration] = useAtom(totalDurationState);
  const [selectedServices] = useAtom(selectedServiceState);
  const [serviceInfo, setServiceInfo] = useState<ServiceInfo>();
  const [successText] = useAtom(successTextState);

  useEffect(() => {
    setServiceInfo({
      startDate: moment(selectedSlot?.date).format('YYYY-MM-DD'),
      serviceName: '',
      startTime: moment
        .tz(selectedSlot?.date, 'Europe/Amsterdam')
        .format('HH:mm'),
      endTime: moment
        .tz(selectedSlot?.date, 'Europe/Amsterdam')
        .add(duration ? duration : 30, 'minutes')
        .format('HH:mm'),
      location: selectedSlot?.location
        ? selectedSlot?.location
        : 'Standort nicht verfügbar Ottostraße 1 , 80333 München',
      duration: convertMinutes(duration),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const location = serviceInfo?.location.split(', O');

  return (
    <div className="flex items-center justify-center flex-col mt-4 px-3">
      <h1 className="text-xl font-bold">
        {/* {successText || 'Dein Termin wurde geplant'} */}
        Ihr Termin ist bestätigt.
      </h1>
      <p className="text-center mt-3 text-gray-500">
        Wir freuen uns auf Ihren Besuch
      </p>
      <div className="w-full">
        <div className="flex flex-col gap-3 md:gap-2 px-3 py-2 w-full my-2">
          <div className="w-2/3 mx-auto">
            <div className="md:flex md:items-center gap-10">
              <div className="flex items-center justify-center gap-2">
                {/* <AiTwotoneCalendar /> */}
                <p>
                  <strong>Datum:</strong>{' '}
                  {moment(serviceInfo?.startDate)
                    .locale('de')
                    .format('Do MMMM YYYY')}{' '}
                </p>
              </div>
            </div>
            <div className="md:flex md:items-center gap-10">
              <div className="flex items-center justify-center gap-2">
                {/* <AiFillClockCircle /> */}
                <p>
                  <span>
                    <strong>Uhrzeit:</strong> {serviceInfo?.startTime} Uhr
                  </span>
                </p>
              </div>
            </div>
            <div className="md:flex md:items-center gap-10">
              <div className="flex items-center justify-center gap-2">
                {/* <AiFillClockCircle /> */}
                <p className="success-s">
                  <span>
                    <strong>Gebuchte Behandlung/en:</strong>{' '}
                    {selectedServices.map((service) => (
                      <span key={service.id} className="success-services">
                        {service.externalName}
                        <span>,</span>{' '}
                      </span>
                    ))}
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-[30px] flex justify-end"></div>
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d84145.45465232129!2d9.176522!3d48.77147600000001!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x84ad4aabe6364d25%3A0x25b5590fa8c12bf1!2sWolanin%20MD%20-%20Aesthetics!5e0!3m2!1sen!2sus!4v1719928049669!5m2!1sen!2sus"
              frameBorder="0"
              className="w-full h-52 md:h-64"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Success;
