import { FC, useEffect, useState } from 'react';
import Header from '../components/atoms/Header';
import Search from '../components/Search';
import Category from '../components/Category';
import { useAtom } from 'jotai';
import {
  activeNumberState,
  currentPageState,
  selectedServiceState,
  servicesState,
  totalDurationState,
} from '../atoms';
import useServiceSelection from '../hooks/useServiceSelection';
import Service from '../components/Service';
import Icon from '../components/atoms/Icon';
import convertMinutes from '../lib/convertMinutes';
import truncateText from '../lib/truncateText';
import convertToDecimal from '../lib/convertToDecimal';

interface AddMoreServicesProps {}

const AddMoreServices: FC<AddMoreServicesProps> = ({}) => {
  // Global State
  const [services] = useAtom(servicesState);
  const [selectedServices, setSelectedServices] = useAtom(selectedServiceState);
  const [currentNumber, setCurrentNumber] = useAtom(activeNumberState);
  const [, setCurrentPage] = useAtom(currentPageState);
  const [, setDuration] = useAtom(totalDurationState);

  // Local State
  const [totalDuration, setTotalDuration] = useState<number>(0);
  const [totalPrice, setTotalPrice] = useState<string>('');

  // Hooks
  const { handleSelectService } = useServiceSelection();

  const removeService = (id: string | number) => {
    const updatedServices = selectedServices
      .filter((service: Service) => service.id !== id)
      .map((service: Service) => {
        return {
          ...service,
          ac_active_value:
            service.ac_active_value > 1
              ? service.ac_active_value - 1
              : service.ac_active_value,
        };
      });
    setSelectedServices(updatedServices);
    setCurrentNumber(currentNumber - 1);
  };

  const handleBackClick = () => {
    setCurrentPage('services');
  };
  const gotoNextPage = () => {
    if (selectedServices.length) {
      setCurrentPage('continue');
    }
  };

  const Continue = () => {
    setCurrentPage('continue');
  };

  useEffect(() => {
    const totalDuration = selectedServices.reduce(
      (acc, curr) => acc + curr.duration,
      0,
    );
    setDuration(totalDuration);
    const totalPrice = selectedServices.reduce(
      (acc, curr) => acc + (curr?.gross ? curr?.gross : 0),
      0,
    );
    setTotalDuration(totalDuration);
    setTotalPrice(convertToDecimal(totalPrice));
  }, [selectedServices]);

  return (
    <div className="add-more-services">
      <main className="wrapper">
        <div className="col-span-2 lg:border-r-2 lg:pr-4">
          <Header title="Dienstleistungen" />
          <div className="w-full md:w-2/3 mt-4">
            <Search />
          </div>
          <Category />
          <button
            className="action continue w-1/3 mx-auto mb-4 block lg:hidden mt-4"
            onClick={gotoNextPage}
          >
            Weiter
          </button>
          <div className="">
            <div className="my-4 grid grid-cols-1 gap-4 overflow-y-auto scrollbar h-[70vh] lg:h-[40vh] pb-24">
              {services.map((service: Service) => (
                <Service
                  handleSelectService={handleSelectService}
                  service={service}
                  key={service.id}
                  activeNumber={selectedServices?.find(
                    (num) => num.id === service.id,
                  )}
                  className="!h-[8rem]"
                />
              ))}
            </div>
          </div>
        </div>
        {/* <div className="col-span-1 hidden lg:block">
          <Header title="Ausgewählte Dienstleistungen" />
          <div className="selected-services-02 scrollbar pb-8">
            {selectedServices.map((service: Service, index: number) => (
              <div className="selected-service" key={service.id}>
                <div className="content">
                  <h2
                    className="text-sm font-semibold"
                    title={service.externalName}
                  >
                    {index + 1}. {truncateText(service.externalName, 30)}
                  </h2>
                  <div className="meta">
                    <span className="data">
                      <Icon icon="mingcute:time-line" className="w-5 h-5" />
                      <span>{service.duration}</span>
                      mins
                    </span>
                    <span className="data">
                      €<span>{convertToDecimal(service?.gross || 0)}</span>
                    </span>
                  </div>
                </div>
                <div className="action">
                  <button onClick={() => removeService(service.id)}>
                    <Icon icon="uil:times" className="w-6" />
                  </button>
                </div>
              </div>
            ))}
            <div className="footer-content">
              <div className="duration">
                <span>Dauer</span>
                <b>{convertMinutes(totalDuration)}</b>
              </div>
              <div className="total">
                <span>Gesamt</span>
                <b>€{totalPrice}</b>
              </div>
            </div>
          </div>
        </div> */}
      </main>
      <div className="main-footer">
        <button className="action" onClick={handleBackClick}>
          Zurück
        </button>
        <button className="continue action" onClick={Continue}>
          Weiter
        </button>
      </div>
    </div>
  );
};

export default AddMoreServices;
