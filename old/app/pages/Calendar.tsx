'use client';
import { FC, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import moment from 'moment';
import 'moment-timezone';

import Calendar from 'react-calendar';
import qs from 'qs';
import request from '@/app/api/request';
import Header from '../components/atoms/Header';
import { useAtom } from 'jotai';
import {
  activeSlotState,
  currentPageState,
  isLoading,
  selectedServiceState,
} from '../atoms';

import '@/app/assets/scss/_calendar.scss';
interface CalendarAppProps {}

const CalendarApp: FC<CalendarAppProps> = () => {
  // Local State
  const [activeDates, setActiveDates] = useState<any>([]);
  const [allSlots, setAllSlots] = useState<null | any[]>(null);
  const [activeCalendarDate, setActiveCalendarDate] = useState(new Date());
  const [slots, setSlots] = useState<any[]>([]);
  const [totalDuration, setTotalDuration] = useState<number>(0);
  const [showSlots, setShowSlots] = useState<boolean>(false);

  // Global State
  const [, setLoading] = useAtom(isLoading);
  const [selectedServices] = useAtom(selectedServiceState);
  const [activeSlot, setActiveSlot] = useAtom(activeSlotState);
  const [, setCurrentPage] = useAtom(currentPageState);

  const calendarProps = () => {
    const props = {
      minDate: new Date(),
      onClickDay: (_value: any, _event: any) => {},
    };

    props.onClickDay = (value, event) => {
      if (allSlots) {
        const serviceIds = selectedServices.map((service) => service.id);
        const uniqueDates = new Set();
        const filteredAppointments: Slot[] = [];
        setShowSlots(true);

        const filteredSlots = allSlots[
          moment(value).format('YYYY-MM-DD') as any
        ].filter((slot: any) => serviceIds.includes(slot.service));

        filteredSlots.forEach((slot: Slot) => {
          if (!uniqueDates.has(slot.date)) {
            uniqueDates.add(slot.date);
            filteredAppointments.push(slot);
          }
        });

        setSlots(filteredAppointments);
      }
    };
    return props;
  };

  const handleMonthChange = (value: any) => {
    setActiveCalendarDate(value.activeStartDate);

    if (allSlots) {
      const lastDate = Object.keys(allSlots)[Object.keys(allSlots).length - 1];
      const formattedDate = moment(value.activeStartDate).format('YYYY-MM-DD');
      const timeToReach = moment(formattedDate).isAfter(lastDate);
      if (timeToReach) {
        // Query
        const newQuery: any = {
          duration: totalDuration,
          start: moment(
            lastDate || moment(value.activeStartDate).format('YYYY-MM-DD'),
          )
            .clone()
            .add(1, 'day')
            .utc()
            .startOf('isoWeek')
            .toISOString(),
        };

        setLoading(true);
        request(`/slots?${qs.stringify(newQuery)}`, {
          method: 'GET',
          headers: {},
        })
          .then((response) => {
            if (allSlots) {
              setAllSlots((prev: any) => ({ ...prev, ...response.data }));
            } else {
              setAllSlots(response.data);
            }
          })
          .catch((error) => {
            toast.error('Entschuldigung, versuchen Sie es später noch einmal');
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  };

  const isDateEnabled = (date: any) => {
    const dateString = moment(date).format('YYYY-MM-DD');
    // @ts-ignore
    return activeDates.includes(dateString);
  };

  const getData = () => {
    const newQuery: any = {
      duration: totalDuration,
    };

    setLoading(true);
    request(`/slots?${qs.stringify(newQuery)}`, { method: 'GET', headers: {} })
      .then((response) => {
        setAllSlots(response.data);
        if (Object.keys(response.data).length > 0) {
          setActiveCalendarDate(new Date(Object.keys(response.data)[0]));
        }
      })
      .catch((error) => {
        toast.error('Entschuldigung, versuchen Sie es später noch einmal');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleActiveSlot = (slot: Slot) => {
    setActiveSlot({ id: slot.id, date: slot.date, service: slot.service });
  };

  const handleBackClick = () => {
    if (showSlots) {
      setShowSlots(false);
      setActiveSlot(null);
    } else {
      setCurrentPage('continue');
    }
  };
  const gotoFormPage = () => {
    setCurrentPage('form');
  };

  useEffect(() => {
    if (allSlots) {
      setActiveDates(Object.keys(allSlots));
    }
  }, [allSlots]);

  useEffect(() => {
    const totalDuration = selectedServices.reduce(
      (acc, curr) => acc + curr.duration,
      0,
    );
    setTotalDuration(totalDuration);
  }, [selectedServices]);

  useEffect(() => {
    if (totalDuration > 0) {
      getData();
    }
  }, [totalDuration]);

  return (
    <div className="calendar">
      <div className="h-[90%]">
        <Header title="Wählen Sie ein Datum und eine Uhrzeit" />
        <div className="main-content">
          <div className="content-calendar scrollbar">
            {!showSlots && (
              <Calendar
                {...calendarProps()}
                tileDisabled={({ date }) => !isDateEnabled(date)}
                onActiveStartDateChange={handleMonthChange}
                locale="de"
                activeStartDate={activeCalendarDate}
              />
            )}
            {showSlots && (
              <div className="slots scrollbar">
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                  {slots &&
                    slots?.map((slot: Slot, i) => {
                      return (
                        <div key={i + 1 + 'slots'} className="slot">
                          <button
                            onClick={() => handleActiveSlot(slot)}
                            className={`${
                              slot.id === activeSlot?.id ? 'active' : ''
                            }`}
                          >
                            {moment
                              .utc(slot.date)
                              .tz('Europe/Amsterdam')
                              .format('HH:mm')}
                          </button>
                        </div>
                      );
                    })}
                  <div className="flex lg:hidden justify-between mb-6">
                    <button className="action" onClick={handleBackClick}>
                      Zurück
                    </button>
                    <button
                      onClick={gotoFormPage}
                      className={`continue action ${
                        activeSlot ? 'active' : ''
                      }`}
                      disabled={activeSlot ? false : true}
                    >
                      Weiter
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="footer">
        <button className="action" onClick={handleBackClick}>
          Zurück
        </button>
        <button
          onClick={gotoFormPage}
          className={`continue action ${activeSlot ? 'active' : ''}`}
          disabled={activeSlot ? false : true}
        >
          Weiter
        </button>
      </div>
    </div>
  );
};

export default CalendarApp;
