// utils/encryption-utils.ts
import { randomBytes, createCipheriv, createDecipheriv } from 'crypto';

const algorithm = 'aes-256-cbc';
const key = Buffer.from(
  '0f7dee46a8e93609b14c3d0ca464ea2b480f75e4b4e4c0d4eaf43d2b67ff30c6',
  'hex',
); // Ensure this is 32 bytes
const ivLength = 16;

export function encryptText(text: string): string {
  const iv = randomBytes(ivLength);
  const cipher = createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(text);
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  return `${iv.toString('hex')}:${encrypted.toString('hex')}`;
}

export function decryptText(text: string): string {
  const [ivHex, encryptedTextHex] = text.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const encryptedText = Buffer.from(encryptedTextHex, 'hex');
  const decipher = createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encryptedText);
  decrypted = Buffer.concat([decrypted, decipher.final()]);
  return decrypted.toString();
}
