.react-calendar {
  @apply max-w-4xl mx-2;
  max-width: 100%;
  background: white;
  font-family: Arial, Helvetica, sans-serif;
  line-height: 1.125em;
}

.react-calendar--doubleView {
  width: 700px;
}

.react-calendar__decade-view__years,
.react-calendar__year-view__months {
  display: grid !important;
  grid-template-columns: auto auto auto;
}

.react-calendar--doubleView .react-calendar__viewContainer {
  display: flex;
  margin: -0.5em;
}

.react-calendar--doubleView .react-calendar__viewContainer > * {
  width: 50%;
  margin: 0.5em;
}

.react-calendar,
.react-calendar *,
.react-calendar *:before,
.react-calendar *:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.react-calendar button {
  border: 1px solid #e8e4da;
  outline: none;
  transition: all 0.2s ease-in-out;
  @apply m-2 lg:m-[4px];
}

.react-calendar button:enabled:hover {
  cursor: pointer;
}

.react-calendar__navigation {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  /* margin: auto;
      width: 230px;
      height: 44px; */
}
.react-calendar__navigation__label {
  background-color: white !important;
  pointer-events: none !important;
}
.react-calendar__navigation__label__labelText {
  font-size: 18px;
  font-weight: bold;
}
.react-calendar__navigation__arrow {
  font-size: xx-large;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white !important;
  border: none !important;
}

.react-calendar__navigation button {
  /* min-width: 44px; */
  background: none;
  border: none;
}
.react-calendar__navigation button:disabled {
  background-color: #ededed;
}

.react-calendar__navigation button:enabled:hover,
.react-calendar__navigation button:enabled:focus {
  background-color: #e6e6e6;
}

.react-calendar__month-view__weekdays {
  text-align: center;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.75em;
}

.react-calendar__month-view__weekdays__weekday {
  padding: 0.5em;
}

.react-calendar__month-view__weekdays__weekday abbr {
  color: #25292c66;
}

.react-calendar__month-view__weekNumbers .react-calendar__tile {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75em;
  font-weight: bold;
}
.react-calendar__month-view__days {
  margin-top: 15px;
  display: grid !important;
  grid-template-columns: auto auto auto auto auto auto auto;
}

/* .react-calendar__month-view__days__day--weekend {
      color: #757575;
      background-color: #e6e6e6 !important;
    }
    
    .react-calendar__month-view__days__day--neighboringMonth {
      color: #757575;
      background-color: #e6e6e6 !important;
    } */

.react-calendar__year-view .react-calendar__tile,
.react-calendar__decade-view .react-calendar__tile,
.react-calendar__century-view .react-calendar__tile {
  padding: 2em 0.5em;
}

.react-calendar__tile {
  @apply px-[6.6667px] py-3 md:py-4 text-center;
  max-width: 100%;
  border-radius: 8px;
  background: none;
  text-align: center;
  line-height: 16px;
}
.react-calendar__tile:hover {
  border: 1px solid black;
}

.react-calendar__tile:disabled {
  background-color: #f0f0f0;
  cursor: default;
}
.react-calendar__tile:disabled abbr {
  color: #25292c66;
}
.react-calendar__tile:disabled:hover {
  border: 1px solid #f0f0f0;
}

/* .react-calendar__tile:enabled:hover,
    .react-calendar__tile:enabled:focus {
      background-color: #e6e6e6;
    } */

.react-calendar__tile--now {
  /* background: #ffff76; */
  position: relative;
}
.react-calendar__tile--now::before {
  position: absolute;
  content: '';
  background: #000000;
  width: 6px;
  height: 6px;
  bottom: 8px;
  right: 50%;
  left: 50%;
  border-radius: 50%;
}

/* .react-calendar__tile--now:enabled:hover,
    .react-calendar__tile--now:enabled:focus {
      background: #ffffa9;
    } */

.react-calendar__tile--hasActive {
  background: #76baff;
}

.react-calendar__tile--hasActive:enabled:hover,
.react-calendar__tile--hasActive:enabled:focus {
  background: #a9d4ff;
}

/* .react-calendar__tile--active {
      background: #006edc;
      color: white;
    } */

/* .react-calendar__tile--active:enabled:hover,
    .react-calendar__tile--active:enabled:focus {
      background: #1087ff;
    } */

.react-calendar--selectRange .react-calendar__tile--hover {
  background-color: #e6e6e6;
}

.react-calendar__navigation__next2-button,
.react-calendar__navigation__prev2-button {
  display: none;
}
.react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none;
}
.checkbox-group {
  user-select: none;
}
.checkbox-group input {
  padding: 0;
  height: initial;
  width: initial;
  margin-bottom: 0;
  display: none;
  cursor: pointer;
}

.checkbox-group label {
  position: relative;
  cursor: pointer;
}

.checkbox-group label:before {
  content: '';
  -webkit-appearance: none;
  background-color: transparent;
  border: 2px solid var(--primary-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
  padding: 9px;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  cursor: pointer;
  margin-right: 12px;
}

.checkbox-group input:checked + label:before {
  background: var(--primary-color);
}

.checkbox-group input:checked + label:after {
  content: '';
  display: block;
  position: absolute;
  top: 3px;
  left: 8px;
  width: 6px;
  height: 14px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
