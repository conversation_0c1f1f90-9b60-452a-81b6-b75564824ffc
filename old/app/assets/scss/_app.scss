.service {
  @apply border rounded-md shadow h-36 flex gap-2;
  .img {
    @apply w-[164px] h-[142px];
    img {
      @apply w-[150px] h-[142px] object-cover rounded-tl-md rounded-bl-md;
    }
  }
  .content {
    @apply flex flex-col justify-between pb-2 pr-2 w-3/5;
    h3 {
      @apply font-semibold text-black pt-2;
    }
    .footer {
      @apply flex justify-between w-full gap-2;
      button {
        @apply bg-primary bg-opacity-20 text-primary px-0.5 md:px-2 py-1 rounded-md disabled:cursor-not-allowed;
        &.inactive {
          @apply bg-white border text-black px-2;
        }
      }
      .meta {
        @apply flex gap-2;
        span.data {
          @apply flex gap-0 items-center text-black;
        }
      }
    }
  }
}

.categories-desktop {
  @apply border-b-2 overflow-x-auto flex-row gap-3 mt-4 hidden md:flex;
  button {
    @apply border-b-2 border-transparent p-2 w-auto min-w-max;
    &.active {
      @apply border-primary text-primary;
    }
  }
}
a.action,
button.action {
  @apply border px-4 py-2 rounded-md bg-white text-xs md:text-sm lg:text-base disabled:cursor-not-allowed;
}
a.continue,
button.continue {
  @apply text-white;
  background-image: radial-gradient(at center center, #616c5d 34%, #054224 96%);
  &:hover {
    background-image: linear-gradient(180deg, #054224 0%, #054224 100%);
  }
  &:disabled {
    background-image: radial-gradient(
      at center center,
      rgba(97, 108, 93, 0.5) 34%,
      rgba(5, 66, 36, 0.5) 96%
    );
  }
}

.booking-widget {
  @apply h-screen flex flex-col items-center mx-4 overflow-hidden;
  .container {
    @apply w-full h-auto border p-1 my-4 relative overflow-hidden rounded-md 2xl:w-[44%] lg:w-[84%] lg:h-[70%] md:p-4;
  }
  .services {
    > .content {
      @apply p-3;
      .search {
        @apply w-full md:w-2/3 lg:w-1/3;
      }
    }
  }
  .main-content {
    .services-wrapper {
      @apply mt-4 overflow-y-auto pb-28 lg:pb-6;
    }
  }
  .add-more-services {
    @apply h-auto lg:h-full flex justify-between flex-col pb-12 lg:pb-4 relative p-3;
    .wrapper {
      @apply h-[88%];
    }
    .selected-services-02 {
      @apply flex flex-col overflow-y-auto h-[50vh];
      .selected-service {
        @apply border-b-2 py-2 flex justify-between items-center pr-4 pl-2;
        h2 {
          @apply mb-4;
        }
        .meta {
          @apply flex gap-2;
          .data {
            @apply flex gap-2;
          }
        }
      }
      .footer-content {
        > div {
          @apply flex justify-between text-xl my-2;
          b {
            @apply flex gap-1;
          }
        }
      }
    }
    .main-footer {
      @apply hidden md:flex justify-between border-t-2 mt-4 py-4 bg-white w-full z-20 relative;
    }
  }
  .confirm-services {
    @apply p-3 h-auto lg:h-full md:pb-6 lg:pb-4;
    .item {
      @apply border rounded-md h-32 lg:h-36 flex justify-between items-center pr-4;
      .content {
        @apply flex gap-2 w-11/12;
        .img {
          @apply h-32 lg:h-36 w-1/3;
          img {
            @apply h-full w-full rounded-tl-md rounded-bl-md object-cover;
          }
        }
        .details {
          @apply w-3/4 flex flex-col justify-between p-2;
          h3 {
            @apply font-medium text-lg text-black;
          }
          .meta {
            @apply flex justify-between text-black;
            .data {
              @apply flex items-center gap-1;
            }
          }
        }
      }
    }
    .total {
      div {
        @apply flex justify-between my-2 text-black;
        b {
          @apply flex gap-1 text-base;
        }
      }
    }
    .footer {
      @apply hidden lg:flex justify-between border-t-2 py-2 bg-white;
    }
  }
  .calendar {
    @apply w-full h-full flex flex-col justify-between p-3;
    .main-content {
      @apply lg:h-[78%] mt-6;
      .content-calendar {
        @apply h-full overflow-y-auto;
      }
      .slots {
        @apply overflow-y-auto mt-2 h-auto max-h-[80vh] pb-0 lg:h-full lg:pb-0;
        .slot {
          @apply w-full block;
          button {
            @apply border-2 w-full rounded-md py-2 hover:text-primary hover:border-primary duration-100;
            &.active {
              @apply bg-primary text-white border-primary;
            }
          }
        }
      }
    }
    .footer {
      @apply hidden lg:flex justify-between bg-white border-t-2 pt-4 lg:py-4 relative z-20;
      button {
        @apply disabled:bg-opacity-50 disabled:cursor-not-allowed;
      }
    }
  }
}

.carts-back {
  @apply -top-[100%] transition-all duration-300;
  &.active {
    @apply top-0;
  }
}
.carts {
  @apply -bottom-[100%] transition-all duration-300;
  &.active {
    @apply bottom-0;
  }
}

// Custom Scroll
.scrollbar::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}
.scrollbar::-webkit-scrollbar-thumb {
  background-color: #999;
  border-radius: 6px;
}

.scrollbar::-webkit-scrollbar-thumb {
  visibility: hidden;
}

.scrollbar:hover::-webkit-scrollbar-thumb {
  visibility: visible;
}

// Mui
.Mui-focused .MuiOutlinedInput-notchedOutline,
.Mui-focused fieldset {
  border-color: #054224 !important;
}
[data-shrink='true'].MuiFormLabel-root,
[data-shrink='true'].MuiFormLabel-root,
.MuiStepLabel-iconContainer.Mui-active.MuiStepLabel-alternativeLabel svg {
  color: #054224 !important;
}
.success-s {
  .success-services {
    @apply mr-1;
    &:last-child {
      > span {
        @apply hidden;
      }
    }
  }
}

.truncate-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
