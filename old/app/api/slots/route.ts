export const dynamic = 'force-dynamic';
export const maxDuration = 60;
import { type NextRequest } from 'next/server';
import moment from 'moment';
import { ccRequest } from '../ccRequest';

/**
 * Get the available slots for a given time range.
 *
 * @param req - The incoming request.
 * @returns An array of available slots.
 */

function groupAppointmentsByDate(
  appointments: Appointment[],
): Record<string, Appointment[]> {
  const groupedAppointments: Record<string, Appointment[]> = {};
  appointments.forEach((appointment) => {
    const date = appointment.date.split('T')[0]; // Extract the date portion
    if (!groupedAppointments[date]) {
      groupedAppointments[date] = [];
    }
    groupedAppointments[date].push(appointment);
  });
  return groupedAppointments;
}

export async function GET(req: NextRequest) {
  const queryParams = req.nextUrl.searchParams;
  try {
    const service = queryParams.get('service');
    const duration = queryParams.get('duration');
    const user = queryParams.get('user');
    const start = queryParams.get('start');
    const end = queryParams.get('end');

    const now = moment();

    const query: any = {
      from: now.add('2', 'hours').toISOString(),
      to: now.clone().add(90, 'days').endOf('day').toISOString(),
    };

    if (duration) {
      query['duration'] = parseInt(duration.toString()) * 60;
    } else {
      query['duration'] = 1800;
    }

    if (service) query['service'] = parseInt(service.toString());

    if (user) query['user'] = parseInt(user.toString());

    if (start) {
      const startDate = moment(start satisfies string);
      query['from'] = startDate.toISOString();
      query['to'] = startDate
        .clone()
        .add(90, 'days')
        .endOf('day')
        .toISOString();
    }

    if (end) query['to'] = moment(end satisfies string).toISOString();

    const result = await ccRequest('/slots', {
      method: 'GET',
      params: query,
    });

    return Response.json({
      status: 200,
      message: 'OK',
      data: groupAppointmentsByDate(result?.slots ?? []),
    });
  } catch (error) {}

  return Response.json({});
}
