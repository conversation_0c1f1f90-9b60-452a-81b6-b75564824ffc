export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { decrypt, encrypt } from '@/app/lib/session';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const cookieStore = cookies();
    const ac_v = cookieStore.get('ac_v')?.value;

    const decryptData: any = await decrypt(ac_v);

    if (decryptData && decryptData?.otp === body.otp) {
      const plainData = {
        otp: Math.floor(100000 + Math.random() * 900000).toString(),
        email: body.email,
      };
      cookieStore.set('logged', await encrypt(plainData), {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production', // ensure this is true in production
        path: '/',
        maxAge: 60 * 60 * 0.5, // 30 minutes in seconds
      });
      return NextResponse.json(
        {
          message: 'Success',
        },
        { status: 200 },
      );
    }

    return NextResponse.json({ message: 'Unable to verify' }, { status: 400 });
  } catch (e: any) {
    return NextResponse.json(
      {
        message: e.toString(),
      },
      { status: 500 },
    );
  }
}
