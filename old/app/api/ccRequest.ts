import qs from 'qs';

export const ccRequest = async (
  url: string,
  options:
    | 'GET'
    | 'POST'
    | 'DELETE'
    | 'PUT'
    | {
        method: 'GET' | 'POST' | 'DELETE' | 'PUT';
        headers?: { [key: string]: string };
        body?: any;
        params?: { [key: string]: string | number | boolean | null };
      },
) => {
  try {
    if (!process.env.CC_API_TOKEN)
      throw new Error('CC Token not found in the environment');

    const requestOptions: {
      method: string;
      body?: any;
      headers?: any;
    } = {
      method: 'GET',
      headers: {
        Authorization: 'Bearer ' + process.env.CC_API_TOKEN,
        'Accept-Encoding': 'application/json',
        'Content-Type': 'application/json',
      },
    };
    let reqUrl = process.env.CC_API_URL + url;
    if (typeof options === 'string') {
      requestOptions.method = options;
    } else {
      requestOptions.method = options.method;
      if (options.body) requestOptions['body'] = JSON.stringify(options.body);
      if (options.headers)
        requestOptions['headers'] = {
          ...requestOptions.headers,
          ...options.headers,
        };
      if (options.params) reqUrl += '?' + qs.stringify(options.params);
    }
    // console.log("Sending request: ", reqUrl, requestOptions);
    const res = await fetch(reqUrl, requestOptions);
    const response = await res.json();
    if (response && response.error) {
      throw response.error.message;
    }
    return response;
  } catch (error) {
    console.log('====================================');
    console.log('Request Error: (In request file) -> ', error);
    console.log('====================================');
    throw error;
    // Just throw
  }
};
