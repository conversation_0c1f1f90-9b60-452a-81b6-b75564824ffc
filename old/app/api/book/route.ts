export const dynamic = 'force-dynamic';
export const maxDuration = 60;
import { ccRequest } from '@/app/api/ccRequest';
import moment from 'moment-timezone';
import { NextResponse, type NextRequest } from 'next/server';

async function mapCCCustomFields(body: any) {
  try {
    const { customFields = null } = await ccRequest('/customFields', 'GET');
    const { email = null, phone = null, message = null } = body;
    const matchedProperties: any = [];
    if (customFields && customFields.length > 0) {
      const apCFNameValue: any = {};
      if (email) apCFNameValue['email'] = email;
      if (phone) apCFNameValue['phoneMobile'] = formatPhoneNumber(phone);
      if (phone) apCFNameValue['phone-mobile'] = formatPhoneNumber(phone);
      if (message) apCFNameValue['note'] = message;

      if (body['tos']) apCFNameValue['newsletter-wanted'] = body['tos'];

      if (body['birthday']) apCFNameValue['Geburtsdatum'] = body['birthday'];

      Object.keys(apCFNameValue).forEach((cf) => {
        // @ts-ignore
        const match = customFields.find(
          (ccf: any) => ccf.name == cf || ccf.label == cf,
        );
        if (match) {
          const value = {
            field: match,
            values: [{ value: apCFNameValue[cf] }],
            patient: null,
          };
          if (match.allowedValues.length > 0) {
            // @ts-ignore
            match.allowedValues.filter((v) => {
              if (v.value == apCFNameValue[cf]) {
                // @ts-ignore
                value.values = [{ id: v.id }];
              }
            });
          }
          matchedProperties.push(value);
        }
      });
    }
    return matchedProperties;
  } catch (error) {}
}

async function retrieveOrCreateCCPatient(body: any) {
  //@ts-ignore
  const { email = null, phone = null } = body;
  let patient = null;
  if (email) {
    patient = await ccRequest('/patients/search?search=' + email, {
      method: 'GET',
    }).then((r) => {
      if (r.patients.length > 0) {
        return r.patients[0];
      }
      return null;
    });
  }

  if (!patient && phone)
    patient = await ccRequest(
      '/patients/search?search=' + formatPhoneNumber(phone),
      'GET',
    ).then((r) => {
      if (r.patients.length > 0) {
        return r.patients[0];
      }
      return null;
    });
  if (!patient) {
    patient = await createPatientToCC(body);
  } else {
    patient = await updatePatientToCC(patient.id, body);
  }
  return patient;
}

async function createPatientToCC(body: any) {
  try {
    const {
      email = null,
      phone = null,
      firstName = null,
      lastName = null,
      birthDay = null,
    } = body;
    if (!email && !phone) {
      throw new Error('Email and phone number is missing.');
    }
    const ccPayload: {
      active: boolean;
      email?: string;
      phoneMobile?: string;
      firstName?: string;
      lastName?: string;
      dob?: string;
      customFields?: any;
    } = {
      active: true,
    };

    if (email) ccPayload['email'] = email;
    if (phone) ccPayload['phoneMobile'] = formatPhoneNumber(phone);
    if (firstName) ccPayload['firstName'] = firstName;
    if (lastName) ccPayload['lastName'] = lastName;
    if (birthDay) ccPayload['dob'] = moment(birthDay).toISOString();
    ccPayload['customFields'] = await mapCCCustomFields(body);
    return await ccRequest('/patients', {
      method: 'POST',
      body: { patient: ccPayload },
    }).then((r) => {
      return r?.patient;
    });
  } catch (error) {}
}
async function updatePatientToCC(patientId: number, body: any) {
  try {
    const {
      email = null,
      phone = null,
      firstName = null,
      lastName = null,
      birthDay = null,
    } = body;
    if (!email && !phone) {
      throw new Error('Email and phone number is missing.');
    }
    const ccPayload: {
      active: boolean;
      email?: string;
      phoneMobile?: string;
      firstName?: string;
      lastName?: string;
      dob?: string;
      customFields?: any;
    } = {
      active: true,
    };
    if (email) ccPayload['email'] = email;
    if (phone) ccPayload['phoneMobile'] = formatPhoneNumber(phone);
    if (firstName) ccPayload['firstName'] = firstName;
    if (lastName) ccPayload['lastName'] = lastName;
    if (birthDay) ccPayload['dob'] = moment(birthDay).toISOString();

    ccPayload['customFields'] = await mapCCCustomFields(body);
    return await ccRequest('/patients/' + patientId, {
      method: 'PUT',
      body: { patient: ccPayload },
    }).then((r) => {
      return r?.patient;
    });
  } catch (error) {}
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    const { slot, duration = 30, user = null, services, message } = body;
    const patient = await retrieveOrCreateCCPatient(body);
    const appointmentSlot = moment(slot);
    const ccAppointmentPayload = {
      slot: false,
      startsAt: appointmentSlot.toISOString(),
      endsAt: appointmentSlot.clone().add(duration, 'minutes').toISOString(),
      patients: [patient.id],
      people: user ? [user] : [],
      services: services,
      description: message,
    };

    const { appointment: ccAppointmentRes = null } = await ccRequest(
      '/appointments',
      {
        method: 'POST',
        body: { appointment: ccAppointmentPayload },
      },
    );

    if (ccAppointmentRes) {
      return NextResponse.json({
        status: 201,
        message: 'Appointment Created',
        appointment: ccAppointmentRes,
      });
    } else {
      throw new Error(`Failed to create CC Appointment`);
    }
  } catch (e: any) {
    // @ts-ignore
    return NextResponse.json({
      status: 500,
      message: e.toString(),
    });
  }
}

function formatPhoneNumber(phoneNumber: string): string {
  const cleanedNumber = phoneNumber.replace(/\D/g, '');
  let countryCode = cleanedNumber.startsWith('49') ? '49' : '';
  const restOfNumber = cleanedNumber.slice(countryCode.length);
  const formattedNumber = `+${countryCode}${restOfNumber}`;
  return formattedNumber;
}
