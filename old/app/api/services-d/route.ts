export const revalidate = 21600;
export const dynamic = "force-dynamic";

import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { ccRequest } from '../ccRequest';

export async function GET(req: NextRequest) {
  try {
    const services = await ccRequest('/services?onlyBooked=true', 'GET').then(
      (d) => d?.services ?? null,
    );
    const productGroups = await ccRequest('/productGroups', 'GET').then(
      (d) => d?.productGroups ?? null,
    );
    const response_data: any = [];
    if (services && productGroups) {
      productGroups.map((pg: any) => {
        const tempS: any = {};
        if (pg.services && pg.services.length > 0) {
          tempS['id'] = pg.id;
          tempS['name'] = pg.name;
          tempS['services'] = [];
          for (const sid in pg.services) {
            const id = pg.services[sid];
            for (const s in services) {
              const ser = services[s];
              if (ser?.id == id) {
                tempS['services'].push(ser);
              }
            }
          }
        }
        if (Object.keys(tempS).length > 0) {
          response_data.push(tempS);
        }
      });
    }

    return NextResponse.json({
      status: 200,
      message: 'OK',
      data: response_data,
    });
  } catch (error: any) {
    return NextResponse.json({
      status: 500,
      message: 'Something went wrong, Please try again.',
    });
  }
}
