export const dynamic = "force-dynamic";
import { NextRequest, NextResponse } from "next/server";
import { ccRequest } from "../../ccRequest";
import moment from "moment";

const cancelAppointmentToCC = async (id: number) => {
  const payload: any = {
    canceledWhy: "cancelled by user",
  };
  const response = await ccRequest(`/appointments/${id}`, {
    method: "PUT",
    body: { appointment: payload },
  });
  return response;
};

function formatPhoneNumber(phoneNumber: string): string {
  const cleanedNumber = phoneNumber.replace(/\D/g, "");
  let countryCode = cleanedNumber.startsWith("49") ? "49" : "";
  const restOfNumber = cleanedNumber.slice(countryCode.length);
  const formattedNumber = `+${countryCode}${restOfNumber}`;
  return formattedNumber;
}
async function mapCCCustomFields(body: any) {
  try {
    const { customFields = null } = await ccRequest("/customFields", "GET");
    const { email = null, phone = null, message = null } = body;
    const matchedProperties: any = [];
    if (customFields && customFields.length > 0) {
      const apCFNameValue: any = {};
      if (email) apCFNameValue["email"] = email;
      if (phone) apCFNameValue["phoneMobile"] = formatPhoneNumber(phone);
      if (phone) apCFNameValue["phone-mobile"] = formatPhoneNumber(phone);
      if (message) apCFNameValue["note"] = message;

      if (body["tos"]) apCFNameValue["newsletter-wanted"] = body["tos"];

      if (body["birthday"]) apCFNameValue["Geburtsdatum"] = body["birthday"];

      Object.keys(apCFNameValue).forEach((cf) => {
        // @ts-ignore
        const match = customFields.find(
          (ccf: any) => ccf.name == cf || ccf.label == cf
        );
        if (match) {
          const value = {
            field: match,
            values: [{ value: apCFNameValue[cf] }],
            patient: null,
          };
          if (match.allowedValues.length > 0) {
            // @ts-ignore
            match.allowedValues.filter((v) => {
              if (v.value == apCFNameValue[cf]) {
                // @ts-ignore
                value.values = [{ id: v.id }];
              }
            });
          }
          matchedProperties.push(value);
        }
      });
    }
    return matchedProperties;
  } catch (error) {}
}

async function updatePatientToCC(patientId: number, body: any) {
  try {
    const {
      email = null,
      phone = null,
      firstName = null,
      lastName = null,
      birthDay = null,
    } = body;
    if (!email && !phone) {
      throw new Error("Email and phone number is missing.");
    }
    const ccPayload: {
      active: boolean;
      email?: string;
      phoneMobile?: string;
      firstName?: string;
      lastName?: string;
      dob?: string;
      customFields?: any;
    } = {
      active: true,
    };
    if (email) ccPayload["email"] = email;
    if (phone) ccPayload["phoneMobile"] = formatPhoneNumber(phone);
    if (firstName) ccPayload["firstName"] = firstName;
    if (lastName) ccPayload["lastName"] = lastName;
    if (birthDay) ccPayload["dob"] = moment(birthDay).toISOString();

    ccPayload["customFields"] = await mapCCCustomFields(body);
    return await ccRequest("/patients/" + patientId, {
      method: "PUT",
      body: { patient: ccPayload },
    }).then((r) => {
      return r?.patient;
    });
  } catch (error) {}
}

export async function PUT(req: NextRequest, route: any) {
  try {
    const { id } = route.params;
    const body = await req.json();

    const {
      patient,
      slot,
      duration = 30,
      user = null,
      services,
      message,
    } = body;
    const appointmentSlot = moment(slot);
    const ccAppointmentPayload = {
      slot: false,
      startsAt: appointmentSlot.toISOString(),
      endsAt: appointmentSlot.clone().add(duration, "minutes").toISOString(),
      patients: [patient],
      people: user ? [user] : [],
      services: services,
      description: message,
    };
    if (body.status === "cancel") {
      const isCancelled = await cancelAppointmentToCC(id);

      return NextResponse.json({
        status: 200,
        isCancelled,
      });
    }

    await updatePatientToCC(patient, body);

    const response = await ccRequest(`/appointments/${id}`, {
      method: "PUT",
      body: { appointment: ccAppointmentPayload },
    });

    return NextResponse.json({
      status: 200,
      message: "Appointments Updated successfully",
      response,
    });
  } catch (e: any) {
    // @ts-ignore
    return NextResponse.json({
      status: 500,
      message: e.toString(),
    });
  }
}
