export const dynamic = "force-dynamic";
export const revalidate = 60;
import { NextRequest, NextResponse } from "next/server";
import { ccRequest } from "../ccRequest";

async function fetchAppointmentsByIds(ids: number[]) {
  const appointmentPromises = ids.map((id) => fetchAppointmentById(id));

  const appointments = await Promise.all(appointmentPromises);
  return appointments;
}

async function fetchAppointmentById(id: number) {
  return new Promise(async (resolve, reject) => {
    try {
      const appointment = await ccRequest(`/appointments/${id}`, {
        method: "GET",
      });
      resolve(appointment);
    } catch (error) {
      reject(error);
    }
  });
}

const searchPatient = async (contact: any) => {
  let patient;
  if (!contact.phone && !contact.email) {
    throw new Error("Invalid data: Email and phone is missing");
  }
  if (contact.email) {
    patient = await ccRequest(`/patients?search=${contact.email}`, {
      method: "GET",
    });
  }
  if (!patient && contact.phone) {
    patient = await ccRequest(`/patients?search=${contact.phone}`, {
      method: "GET",
    });
  }
  return patient;
};

export async function GET(req: NextRequest) {
  try {
    const queryParams = req.nextUrl.searchParams;
    const email = queryParams.get("email") as string;
    const phone = queryParams.get("phone") as string;

    const patient = await searchPatient({ email: email, phone: phone });
    if (patient.patients.length) {
      const appointments = await fetchAppointmentsByIds(
        patient.patients[0].appointments
      );

      return NextResponse.json({
        appointments,
        patient: patient.patients[0],
      });
    }
    return NextResponse.json(
      {
        message: "Not Found",
      },
      { status: 404 }
    );
  } catch (error: any) {
    return NextResponse.json(
      {
        error,
      },
      { status: 500 }
    );
  }
}
