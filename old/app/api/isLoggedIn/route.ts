export const dynamic = 'force-dynamic';
export const maxDuration = 60;
import { NextResponse, type NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { decrypt } from '@/app/lib/session';

export async function GET(req: NextRequest) {
  const cookieStore = cookies();
  try {
    const isLogged = await decrypt(cookieStore.get('logged')?.value);

    if (isLogged) {
      return NextResponse.json(
        {
          message: 'Already logged in',
          email: isLogged.email,
        },
        { status: 200, headers: {} },
      );
    }

    return Response.json({}, { status: 401 });
  } catch (error) {}

  return Response.json({});
}
