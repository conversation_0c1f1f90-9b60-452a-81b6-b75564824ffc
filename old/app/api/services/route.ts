import { NextResponse } from "next/server";
import { type NextRequest } from "next/server";
import { ccRequest } from "../ccRequest";

/**
 * Type for a service object.
 */
interface Service {
  id: string;
  name: string;
  duration: number;
  price: string;
  groupNames?: string[];
  productGroup?: string;
  [key: string]: any;
}

/**
 * Type for a product group object.
 */
interface ProductGroup {
  id: string;
  name: string;
  services?: Service[];
}

/**
 * GET handler for fetching and grouping services.
 * @param req Next.js request object
 * @returns JSON response with grouped and ungrouped services
 */
export async function GET(req: NextRequest) {
  try {
    const requestOptions = {
      method: "GET",
      headers: {
        Authorization: "Bearer " + process.env.CC_PUBLIC_TOKEN,
        "Accept-Encoding": "application/json",
        "Content-Type": "application/json",
      },
    };

    // Fetch product groups and online services in parallel
    const [productGroupsRes, onlineServicesRes] = await Promise.all([
      ccRequest("/productGroups", "GET"),
      fetch("https://scheduler.clinicore.eu/api/scheduler/services", requestOptions).then((res) => res.json()),
    ]);

    const productGroups: ProductGroup[] = productGroupsRes?.productGroups ?? [];
    const onlineServices: Service[] = onlineServicesRes?.services ?? [];

    // Group online services by group name
    const groupNameMap: Record<string, Service[]> = {};
    onlineServices.forEach((service) => {
      if (service.groupNames) {
        service.groupNames.forEach((gName: string) => {
          if (!groupNameMap[gName]) groupNameMap[gName] = [];
          groupNameMap[gName].push(service);
        });
      }
    });

    // Prepare newData array for grouped services
    const newData = Object.keys(groupNameMap)
      .filter((key) => key.trim().length)
      .map((key) => {
        const group = productGroups.find(pg => pg.name === key);
        return {
          name: key,
          id: group ? group.id : undefined, // use group id if found
          services: groupNameMap[key].map((service) => ({
            ...service,
            duration: service.duration / 60,
            gross: parseFloat(service.price),
            externalName: service.name,
          })),
        };
      });

    // Fetch all services from ccRequest
    const allServicesRes = await ccRequest("/services", "GET");
    const allServices: Service[] = allServicesRes?.services ?? [];

    // Filter services to only those that exist in onlineServices
    const onlineServiceIds = new Set(onlineServices.map((os) => os.id));
    const services = allServices.filter((s) => onlineServiceIds.has(s.id));

    // Group services by productGroup, collect ungrouped
    const productGroupMap: Record<string, ProductGroup> = {};
    productGroups.forEach((pg) => {
      productGroupMap[pg.id] = { ...pg, services: [] };
    });
    const unGroupedServices: Service[] = [];
    let groupedCount = 0;
    services.forEach((service) => {
      if (service.productGroup && productGroupMap[service.productGroup]) {
        productGroupMap[service.productGroup].services!.push(service);
        groupedCount++;
      } else {
        unGroupedServices.push(service);
      }
    });

    return NextResponse.json({
      status: 200,
      message: "OK",
      data: newData
    });
  } catch (error: any) {
    // Optionally log error here for debugging
    return NextResponse.json({
      status: 500,
      message: "Something went wrong, Please try again.",
      error: error?.message || error,
    });
  }
}
