export const dynamic = "force-dynamic";

import EmailTemplate from "@/app/components/EmailTemplate";
import { encrypt } from "@/app/lib/session";
import { NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";
import { cookies } from "next/headers";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const cookieStore = cookies();
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const payload = {
      from: "autoPatient <<EMAIL>>",
      to: body.to,
      subject: "Bestätigen Sie Ihre E-Mail Adresse",
      react: EmailTemplate({ otp }),
    };

    const token = await encrypt({ email: body.to, otp });

    const { data, error } = await resend.emails.send(payload);

    if (error) {
      return NextResponse.json({ error }, { status: 400 });
    }

    cookieStore.set("ac_v", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production", // ensure this is true in production
      path: "/",
      maxAge: 60 * 60 * 1, // 1 hour in seconds
    });

    return NextResponse.json(
      {
        message: "Email Sent successfully",
        data: {
          token,
        },
      },
      { status: 201, headers: {} }
    );
  } catch (e: any) {
    return NextResponse.json(
      {
        message: e.toString(),
      },
      { status: 500 }
    );
  }
}
