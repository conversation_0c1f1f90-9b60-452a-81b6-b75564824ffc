'use client';
import { FC, useEffect, useState } from 'react';
import ManageAppointments from '../components/ManageAppointments';
import request from '@/app/api/request';
import { useRouter } from 'next/navigation';

interface pageProps {}

const Page: FC<pageProps> = ({}) => {
  const [email, setEmail] = useState('');
  const router = useRouter();

  useEffect(() => {
    request('/isLoggedIn', { method: 'GET' })
      .then((r) => {
        setEmail(r.email);
      })
      .catch((error) => {
        router.push('/auth');
      });
  }, []);

  return (
    <div className="relative">
      <div className="booking-widget">
        <div className="container">
          {email ? (
            <ManageAppointments data={{ email: email, phone: '' }} />
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
};

export default Page;
