import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import '@/app/assets/scss/_app.scss';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Wolanin MD Aesthetics Stuttgart',
  description:
    'WOLANIN MD - AESTHETICS Ihre Ärztin  Expertin  Für Ästhetische Medizin Ihre Ärztin & Expertin Für Ästhetische Medizin Herzlich Willkommen bei Wolanin MD - Aesthetics, Ihrer führenden Adresse für ästhetische Medizin in Stuttgart. Mein Name ist Malgo Wolanin, ich bin Fachärztin für Allgemeinchirurgie und Expertin für Ästhetische Medizin. TERMIN VEREINBAREN WOLANIN MD',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  );
}
