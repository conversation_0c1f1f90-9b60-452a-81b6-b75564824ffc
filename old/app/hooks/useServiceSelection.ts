import { useAtom } from 'jotai';
import {
  activeNumberState,
  currentPageState,
  selectedServiceState,
  servicesState,
} from '@/app/atoms';

const useServiceSelection = () => {
  const [services] = useAtom(servicesState);
  const [selectedService, setSelectedService] = useAtom(selectedServiceState);
  const [currentPage, setCurrentPage] = useAtom(currentPageState);
  const [activeNumber, setActiveNumber] = useAtom(activeNumberState);

  const handleSelectService = (serviceId: string | number) => {
    const existingServiceIndex = selectedService.findIndex(
      (service: Service) => service.id === serviceId,
    );

    if (existingServiceIndex !== -1) {
      // Service is already selected, remove it
      const updatedServices = selectedService.filter(
        (service: Service) => service.id !== serviceId,
      );
      setSelectedService(updatedServices);
    } else {
      // Service is not selected, add it
      setActiveNumber(activeNumber + 1);
      const newService: any = services.find(
        (service: Service) => service.id === serviceId,
      );

      if (newService) {
        const selectedServices = [
          ...selectedService,
          { ...newService, ac_active_value: activeNumber },
        ];
        setSelectedService(selectedServices);
        // if (currentPage === 'services') {
        //   setCurrentPage('continue');
        // }
      }
    }
  };

  return {
    handleSelectService,
  };
};

export default useServiceSelection;
