import { FC, useEffect, useState } from 'react';
import { useAtom } from 'jotai';
import {
  activeNumberState,
  currentPageState,
  selectedServiceState,
} from '../atoms';
import convertMinutes from '../lib/convertMinutes';
import Icon from './atoms/Icon';
import truncateText from '../lib/truncateText';
import convertToDecimal from '../lib/convertToDecimal';
import Image from 'next/image';

interface FooterDetailsProps {}

const FooterDetails: FC<FooterDetailsProps> = ({}) => {
  // Global State
  const [selectedServices, setSelectedServices] = useAtom(selectedServiceState);
  const [, setCurrentPage] = useAtom(currentPageState);
  const [currentNumber, setCurrentNumber] = useAtom(activeNumberState);

  // Local State
  const [totalDuration, setTotalDuration] = useState<number>(0);
  const [totalPrice, setTotalPrice] = useState<string>('');
  const [showCart, setShowCart] = useState<boolean>(false);

  const handleOpenCarts = () => {
    setShowCart(!showCart);
  };
  const gotoNextPage = () => {
    if (selectedServices.length) {
      setCurrentPage('continue');
      setShowCart(false);
    }
  };

  const removeService = (id: string | number) => {
    setSelectedServices(
      selectedServices.filter((service: Service) => service.id !== id),
    );
    setCurrentNumber(currentNumber - 1);
  };

  useEffect(() => {
    const totalDuration = selectedServices.reduce(
      (acc, curr) => acc + curr.duration,
      0,
    );
    const totalPrice = selectedServices.reduce(
      (acc, curr) => acc + (curr?.gross ? curr?.gross : 0),
      0,
    );
    setTotalDuration(totalDuration);
    setTotalPrice(convertToDecimal(totalPrice));
    if (selectedServices.length === 0) {
      setCurrentPage('services');
    }
  }, [selectedServices]);

  return (
    <div className="block md:hidden">
      {showCart && (
        <div
          className={`w-full fixed top-0 bg-white border-b z-20 p-2 carts-back ${
            showCart ? 'active' : ''
          }`}
        >
          <button className="action" onClick={handleOpenCarts}>
            Zurück
          </button>
        </div>
      )}
      <div
        className={`carts w-full bg-white fixed h-[93vh] z-20 border-t ${
          showCart ? 'active' : ''
        }`}
      >
        <div className="flex flex-col justify-between h-full">
          <div className="flex flex-col gap-4 h-[90%] overflow-y-auto scrollbar p-3">
            {selectedServices.map((service, index: number) => (
              <div className="border p-2 rounded-md" key={service.id}>
                <div className="flex gap-2">
                  <Image
                    src={`/images/${service.id}.png`}
                    alt=""
                    className="h-20 w-20 rounded"
                    width={200}
                    height={200}
                  />
                  <div className="flex flex-col justify-between w-full">
                    <h2 className="font-medium" title={service.externalName}>
                      {index + 1}. {truncateText(service.externalName, 30)}
                    </h2>
                    <div className="flex justify-between items-center">
                      <div className="flex gap-2">
                        <span className="flex items-center">
                          <Icon icon="mingcute:time-line" className="w-5 h-5" />
                          &nbsp;<span>{service.duration}</span>
                          Min.
                        </span>
                        <span className="flex gap-1 items-center">
                        ab € {convertToDecimal(service?.gross || 0)}
                        </span>
                      </div>
                      <button onClick={() => removeService(service.id)}>
                        <Icon icon="uil:times" className="w-6" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {selectedServices.length ? (
              <button
                className="action continue w-2/3 mx-auto mb-4 block lg:hidden"
                onClick={gotoNextPage}
              >
                Weiter
              </button>
            ) : (
              ''
            )}
            {selectedServices.length === 0 ? (
              <p className="w-full h-full flex justify-center items-center">
                Es wurde kein Dienst ausgewählt.
              </p>
            ) : (
              ''
            )}
          </div>
          {selectedServices.length ? (
            <button
              className="action continue w-2/3 mx-auto mb-4 hidden lg:block"
              onClick={gotoNextPage}
            >
              Weiter
            </button>
          ) : (
            ''
          )}
        </div>
      </div>
      {!showCart && (
        <div className="fixed w-full bg-primary h-fit bottom-0 p-3 rounded-t-xl text-white z-20 text-xs py-4">
          <div className="flex justify-between">
            <div className="flex gap-2">
              <span>{selectedServices.length} Artikel</span>
              <span className="flex">ab € {totalPrice}</span>
              <span>({convertMinutes(totalDuration)})</span>
            </div>
            <button onClick={handleOpenCarts}>Warenkorb ansehen</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FooterDetails;
