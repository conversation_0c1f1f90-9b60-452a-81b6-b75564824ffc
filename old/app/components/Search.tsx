import { FC, useState } from 'react';
import { useAtom } from 'jotai';
import {
  activeCategoryState,
  allServicesState,
  filteredDataState,
  servicesState,
} from '@/app/atoms';

interface SearchProps {}

const Search: FC<SearchProps> = ({}) => {
  // Global State
  const [allServices, _] = useAtom(allServicesState);
  const [__, setFilteredData] = useAtom(filteredDataState);
  const [, setServices] = useAtom(servicesState);
  const [, setActiveCategory] = useAtom(activeCategoryState);

  //   Local State
  const [query, setQuery] = useState<string>('');

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchQuery = event.target.value;
    setQuery(searchQuery);

    if (!searchQuery.trim()) {
      setFilteredData(allServices);
      // @ts-ignore
      setServices(allServices[0]?.services ? allServices[0]?.services : []);
      setActiveCategory(allServices[0].id)
    } else {
      const searchResults = allServices
        .filter((parent: any) =>
          parent.services.some((service: any) =>
            service.externalName
              .toLowerCase()
              .includes(searchQuery.toLowerCase()),
          ),
        )
        .map((parent: any) => ({
          ...parent,
          services: parent.services.filter((service: any) =>
            service.externalName
              .toLowerCase()
              .includes(searchQuery.toLowerCase()),
          ),
        }));

      if (searchResults.length === 0) {
        setServices([]);
      }else{
        setServices(searchResults[0]?.services? searchResults[0]?.services : []);
        // @ts-ignore
        setActiveCategory(searchResults[0]?.id);
      }
      setFilteredData(searchResults as any);
    }
  };

  return (
    <div className="search">
      <input
        type="text"
        name="search"
        placeholder="Suche nach Services"
        required
        className="w-full h-10 focus:outline-none border border-[#727272] rounded-md p-2"
        onChange={handleSearch}
        value={query}
      />
    </div>
  );
};

export default Search;
