'use client';

import { FC } from 'react';
import { useAtom } from 'jotai';
import { selectedServiceState, servicesState } from '@/app/atoms';
import useServiceSelection from '@/app/hooks/useServiceSelection';
import Service from './Service';

interface ServicesContentProps {}

const ServicesContent: FC<ServicesContentProps> = ({}) => {
  // Global State
  const [services] = useAtom(servicesState);
  const [selectedServices] = useAtom(selectedServiceState);
  const { handleSelectService } = useServiceSelection();

  return (
    <div className="h-screen relative">
      <div className="services-wrapper overflow-y-auto h-[90%] lg:h-[50%] scrollbar">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mb-12 lg:mb-4">
          {services.map((service: Service) => (
            <Service
              handleSelectService={handleSelectService}
              service={service}
              key={service.id}
              activeNumber={selectedServices?.find(
                (num) => num.id === service.id,
              )}
            />
          ))}
        </div>
      </div>
      {services.length === 0 ? (
        <div className="w-full h-full text-center absolute top-0 pt-20">
          {services.length === 0 ? 'Keine Dienste gefunden' : ''}
        </div>
      ) : (
        ''
      )}
    </div>
  );
};

export default ServicesContent;
