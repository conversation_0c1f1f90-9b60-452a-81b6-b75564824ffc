import { FC, useEffect, useRef, useState } from 'react';
import request from '@/app/api/request';
import { gotoManageAppointmentsState } from '../atoms';
import { useAtom } from 'jotai';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import Preloader from './atoms/Preloader';
import '@/app/assets/scss/otp.css';

interface OTPComponentProps {
  email: string;
}

const OTPComponent: FC<OTPComponentProps> = ({ email }) => {
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);
  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const [otp, setOtp] = useState(Array(6).fill(''));
  const [, setGotoManageAppointments] = useAtom(gotoManageAppointmentsState);
  const [loading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number>(60);
  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(true);
  const router = useRouter();

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
    const pastedValue = event.clipboardData
      .getData('text')
      .slice(0, otp.length);
    const updatedOtp = otp.map((val, index) =>
      pastedValue[index] ? pastedValue[index] : '',
    );
    setOtp(updatedOtp);
    updatedOtp.forEach((value, index) => {
      if (inputRefs.current[index]) {
        inputRefs.current[index]!.value = value;
        if (value) {
          inputRefs.current[index]!.removeAttribute('disabled');
        }
      }
    });
    if (inputRefs.current[updatedOtp.length - 1]) {
      inputRefs.current[updatedOtp.length - 1]!.focus();
    }
    updateButtonState(updatedOtp);
  };

  const handleChange = (
    index: number,
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { value } = event.target;
    if (value.length > 1) return;

    const updatedOtp = [...otp];
    updatedOtp[index] = value;
    setOtp(updatedOtp);

    if (value && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]!.removeAttribute('disabled');
      inputRefs.current[index + 1]!.focus();
    }

    if (!value && inputRefs.current[index - 1]) {
      inputRefs.current[index - 1]!.focus();
    }

    updateButtonState(updatedOtp);
  };

  const handleKeyUp = (
    index: number,
    event: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (event.key === 'Backspace') {
      const updatedOtp = [...otp];
      updatedOtp[index] = '';
      setOtp(Array(6).fill(''));

      // Move focus to the previous input and enable it
      if (inputRefs.current[index - 1]) {
        inputRefs.current[index - 1]!.focus();
        inputRefs.current[index]!.setAttribute('disabled', 'disabled');
      }

      // Update button state
      updateButtonState(updatedOtp);
    }
  };

  const updateButtonState = (updatedOtp: string[]) => {
    if (buttonRef.current) {
      const allFilled = updatedOtp.every((char) => char !== '');
      buttonRef.current.disabled = !allFilled;
      buttonRef.current.classList.toggle('active', allFilled);
    }
  };

  const submitOTP = async () => {
    const otpValue = otp.join('');
    try {
      const payload: any = {
        otp: otpValue,
        email: email,
      };
      await request('/verify-otp', {
        method: 'POST',
        body: payload,
      });
      setGotoManageAppointments(true);
      router.push(`/my-appointments`);
    } catch (error) {
      toast.error(
        'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
      );
    }
  };

  const resendOTP = async () => {
    setLoading(true);
    const payload: any = {
      to: email,
    };

    try {
      await request('/send-email', {
        method: 'POST',
        body: payload,
      });
      setIsResendDisabled(true);
      setTimeLeft(60);
    } catch (error) {
      toast.error(
        'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0]!.focus();
    }
    if (buttonRef.current) {
      buttonRef.current.disabled = true;
    }
  }, []);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setIsResendDisabled(false);
    }
  }, [timeLeft]);

  return (
    <div className="row justify-content-center">
      <div className="col-12 col-md-6 col-lg-4">
        <div className="card bg-white mb-5 mt-5 border-0">
          <div className="card-body p-5 text-center">
            <h4 className="font-bold text-2xl">Verifizierung</h4>
            <p className="text-sm mb-4">
              Ihr Code wurde Ihnen per E-Mail zugesandt
            </p>
            <div className="otp-field mb-4">
              {otp.map((_, index) => (
                <input
                  key={index}
                  type="number" // Changed to text to allow pasting
                  maxLength={1}
                  // @ts-ignore
                  ref={(el) => (inputRefs.current[index] = el)}
                  value={otp[index]}
                  onPaste={handlePaste}
                  onChange={(e) => handleChange(index, e)}
                  onKeyUp={(e) => handleKeyUp(index, e)}
                  disabled={index !== 0 && otp[index] === ''}
                />
              ))}
            </div>

            <button
              className="action continue mb-2"
              ref={buttonRef}
              onClick={submitOTP}
            >
              Bestätigen
            </button>

            <p className="resend text-muted mb-0">
              Sie haben den Code nicht erhalten?{' '}
              <button
                className="text-[#054224] font-semibold disabled:cursor-not-allowed"
                onClick={resendOTP}
                disabled={isResendDisabled}
              >
                {timeLeft > 0
                  ? `OTP erneut senden in ${timeLeft}`
                  : 'Fordern Sie ihn erneut an.'}
              </button>
              
            </p>
          </div>
        </div>
      </div>
      <Preloader isLoading={loading} />
    </div>
  );
};

export default OTPComponent;
