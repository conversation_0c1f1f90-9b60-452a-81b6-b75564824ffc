import { FC } from 'react';

interface CheckboxProps {
  onClick: (id: number | string) => void;
  id: number | string;
  checked: boolean;
}

const Checkbox: FC<CheckboxProps> = ({ onClick, id, checked }) => {
  return (
    <div className="">
      <input
        id="remember-me"
        name="remember-me"
        type="checkbox"
        className="h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
        onChange={() => onClick(id)}
        checked={checked}
      />
      <label
        htmlFor="remember-me"
        className="ml-3 block text-sm leading-6 text-gray-900"
      ></label>
    </div>
  );
};

export default Checkbox;
