import { FC } from 'react';
import Icon from './Icon';

interface PreloaderProps {
  isLoading: boolean;
}

const Preloader: FC<PreloaderProps> = ({ isLoading }) => {
  return (
    <>
      {' '}
      {isLoading && (
        <div className="w-full h-full bg-white bg-opacity-60 z-50 top-0 left-0 flex justify-center items-center absolute">
          <Icon icon="line-md:loading-twotone-loop" />
        </div>
      )}
    </>
  );
};

export default Preloader;
