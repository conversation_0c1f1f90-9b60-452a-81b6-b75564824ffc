'use client';

import { FC, useEffect } from 'react';
import { useAtom } from 'jotai';
import { FormControl, InputLabel, MenuItem } from '@mui/material';
import {
  activeCategoryState,
  filteredDataState,
  servicesState,
} from '@/app/atoms';
import Select from '@mui/material/Select';

interface CategoryProps {}

const Category: FC<CategoryProps> = ({}) => {
  // Global State
  const [filteredData] = useAtom(filteredDataState);
  const [, setServices] = useAtom(servicesState);
  const [activeCategory, setActiveCategory] = useAtom(activeCategoryState);

  const handleServices = (id: string | number) => {
    setActiveCategory(id);
    const category: any = filteredData.find(
      (category: Category) => id === category.id,
    );

    setServices(category.services as any);
  };

  useEffect(() => {
    if (!activeCategory) {
      const categoryWithServices: any = filteredData.find(
        (category: Category) => category.services.length > 0,
      );
      if (categoryWithServices) {
        setActiveCategory(categoryWithServices.id);
        setServices(categoryWithServices.services);
      }
    }
  }, [filteredData]);

  return (
    <div>
      <div className="categories-desktop scrollbar pb-[5px] hidden md:block">
        {/* <button
          onClick={() => handleServices(0)}
          className={`${activeCategory === 0 ? 'active' : ''}`}
        >
          Alle
        </button> */}
        {filteredData.map((category: Category) => (
          <button
            key={category.id}
            onClick={() => handleServices(category.id)}
            className={`${activeCategory === category.id ? 'active' : ''}`}
          >
            {category.name}
          </button>
        ))}
      </div>
      <div className="mobile-categories block md:hidden mt-4">
        <FormControl fullWidth>
          <InputLabel id="category-select-label"></InputLabel>
          <Select
            value={activeCategory}
            onChange={(event) => handleServices(event.target.value as number)}
            defaultValue={activeCategory}
            displayEmpty
            inputProps={{ 'aria-label': 'Without label' }}
          >
            {/* <MenuItem value={0} onClick={() => handleServices(0)}>
              Alle
            </MenuItem> */}
            {filteredData?.map((category: Category) => {
              return (
                <MenuItem
                  key={category.id}
                  value={category.id}
                  onClick={() => handleServices(category.id)}
                >
                  {category.name}
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
      </div>
    </div>
  );
};

export default Category;
