import React from 'react';

interface EmailTemplateProps {
  otp: string;
}

const EmailTemplate: React.FC<EmailTemplateProps> = ({ otp }) => {
  return (
    <html dir="ltr" lang="en">
      <head>
        <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
        <meta name="x-apple-disable-message-reformatting" />
      </head>
      <body
        style={{
          backgroundColor: "#ffffff",
          margin: "0 auto",
          fontFamily:
            "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
        }}
      >
        <table
          align="center"
          width="100%"
          border={0}
          cellPadding={0}
          cellSpacing={0}
          role="presentation"
          style={{ maxWidth: "37.5em", margin: "0 auto", padding: "0px 20px" }}
        >
          <tbody>
            <tr style={{ width: "100%" }}>
              <td>
                <h1
                  style={{
                    color: "#1d1c1d",
                    fontSize: "30px",
                    fontWeight: 700,
                    margin: "20px 0",
                    padding: 0,
                    lineHeight: "42px",
                    textAlign: "center",
                  }}
                >
                  Bestätigen Sie Ihre E-Mail Adresse
                </h1>
                <p
                  style={{
                    fontSize: "20px",
                    lineHeight: "28px",
                    margin: "16px 0",
                    marginBottom: "30px",
                  }}
                >
                  Ihr Bestätigungscode steht unten - geben Sie ihn in Ihr geöffnetes Browserfenster ein.
                </p>
                <table
                  align="center"
                  width="100%"
                  border={0}
                  cellPadding={0}
                  cellSpacing={0}
                  role="presentation"
                  style={{
                    background: "rgb(245, 244, 245)",
                    borderRadius: "4px",
                    marginBottom: "30px",
                    padding: "40px 10px",
                  }}
                >
                  <tbody>
                    <tr>
                      <td>
                        <p
                          style={{
                            fontSize: "30px",
                            lineHeight: "24px",
                            margin: "16px 0",
                            textAlign: "center",
                            verticalAlign: "middle",
                          }}
                        >
                          {otp}
                        </p>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <p
                  style={{
                    fontSize: "14px",
                    lineHeight: "24px",
                    margin: "16px 0",
                    color: "#000",
                  }}
                >
                  Wenn Sie diese E-Mail nicht angefordert haben, brauchen Sie sich keine Sorgen zu machen, Sie können sie gefahrlos ignorieren.
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </body>
    </html>
  );
};

export default EmailTemplate;
