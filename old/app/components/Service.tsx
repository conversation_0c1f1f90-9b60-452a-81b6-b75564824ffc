'use client';

import { FC } from 'react';
import { useAtom } from 'jotai';
import Icon from './atoms/Icon';
import { currentPageState } from '@/app/atoms';
import { cn } from '@/app/lib/cn';
import truncateText from '../lib/truncateText';
import convertToDecimal from '../lib/convertToDecimal';
import Image from 'next/image';
import Checkbox from './atoms/Checkbox';
import removeHtmlTags from '../lib/removeHtmlTags';

type ActiveNumber = {
  ac_active_value: number;
};

interface ServiceProps {
  service: Service;
  handleSelectService: (serviceId: number | string) => void;
  activeNumber: ActiveNumber | Service | undefined;
  className?: string;
}

const Service: FC<ServiceProps> = ({
  service,
  handleSelectService,
  activeNumber,
  className,
}) => {
  const [currentPage] = useAtom(currentPageState);
  return (
    <div className={cn('service', className)} key={service.id}>
      <div className="img">
        <Image
          src={`/images/${service.id}.png`}
          alt=""
          width={156}
          height={156}
          quality={90}
        />
      </div>
      <div className="content overflow-hidden">
        <div>
          <h3 className="font-semibold truncate-2-lines" title={service.externalName}>
            {service.externalName}
          </h3>
          <p
            className="text-sm truncate-2-lines"
            title={removeHtmlTags(service.description || '')}
          >
            {removeHtmlTags(service.description || '')}
          </p>
        </div>
        <div className="footer text-xs md:text-sm">
          <div className="meta">
            <span className="data">
              <Icon icon="mingcute:time-line" className="w-5 h-5" />
              <span>{service.duration}</span>
              Min.
            </span>
            {(currentPage === 'services' ||
              currentPage === 'addMoreServices') && (
              <span className="data">
                ab €<span>{convertToDecimal(service.gross || 0)}</span>
              </span>
            )}
          </div>
          {currentPage !== 'services' && currentPage !== 'addMoreServices' ? (
            <span className="flex justify-between gap-1">
              ab €<span>{convertToDecimal(service.gross || 0)}</span>
            </span>
          ) : (
            <Checkbox
              id={service.id}
              onClick={handleSelectService}
              checked={activeNumber?.ac_active_value ? true : false}
            />
            // <button
            //   onClick={() => handleSelectService(service.id)}
            //   className={`${activeNumber?.ac_active_value ? 'inactive' : ''}`}
            //   disabled={activeNumber?.ac_active_value ? true : false}
            // >
            //   {activeNumber?.ac_active_value || 'Hinzufügen'}
            // </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Service;
