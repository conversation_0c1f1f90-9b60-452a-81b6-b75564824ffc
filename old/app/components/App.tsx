'use client';

import { FC, useEffect, useState } from 'react';
import { useAtom } from 'jotai';
import { Toaster } from 'react-hot-toast';

import Services from '../pages/Services';
import {
  activeNumberState,
  appointmentServicesState,
  currentPageState,
  formDataState,
  isLoading,
  selectedServiceState,
  servicesState,
} from '../atoms';
import AddMoreServices from '../pages/AddMoreServices';
import Preloader from './atoms/Preloader';
import Confirm from '../pages/Confirm';
import Calendar from '../pages/Calendar';
import Form from '../pages/Form';
import Success from '../pages/Success';
import useServiceSelection from '../hooks/useServiceSelection';
import request from '@/app/api/request';

interface AppProps {}

const App: FC<AppProps> = ({}) => {
  const [, setSelectedService] = useAtom(selectedServiceState);
  const [email, setEmail] = useState('');

  // Global variables
  const [currentPage] = useAtom(currentPageState);
  const [loading] = useAtom(isLoading);
  const [servicesIds] = useAtom(appointmentServicesState);
  const [services] = useAtom(servicesState);
  const [activeNumber, setActiveNumber] = useAtom(activeNumberState);
  const [formData, setFormData] = useAtom(formDataState);

  useEffect(() => {
    if (servicesIds.length && services.length) {
      servicesIds.forEach((id) => {
        const newService: any = services.find(
          (service: Service) => service.id === id,
        );
        if (newService) {
          setActiveNumber(activeNumber + 1);
          setSelectedService((prev) => [
            ...prev,
            { ...newService, ac_active_value: activeNumber },
          ]);
        }
      });
    }
  }, [servicesIds, services]);

  useEffect(() => {
    request('/isLoggedIn', { method: 'GET' })
      .then((r) => {
        setEmail(r.email);
      })
      .catch((error) => {});
  }, []);

  useEffect(() => {
    if (!formData.email && email) {
      request(`/appointments?email=${email}`)
        .then((response: any) => {
          const { patient } = response;
          setFormData({
            firstName: patient.firstName,
            lastName: patient.lastName,
            email: patient.email,
            phone: patient.phoneMobile,
            id: patient.id,
          });
        })
        .catch((error) => {})
        .finally(() => {});
    }
  }, [formData, email]);

  return (
    <div className="relative">
      <div className="booking-widget">
        <div className="container">
          {currentPage === 'services' && (
            <Services isLoggedIn={email ? true : false} />
          )}
          {/* {currentPage === 'selectedServices' && <SelectedServices />} */}
          {currentPage === 'addMoreServices' && <AddMoreServices />}
          {currentPage === 'continue' && <Confirm />}
          {currentPage === 'calendar' && <Calendar />}
          {currentPage === 'form' && <Form />}
          {currentPage === 'success' && <Success />}
          <Preloader isLoading={loading} />
        </div>
      </div>
      {/* {currentPage === 'addMoreServices' && <FooterDetails />} */}
      <Toaster />
    </div>
  );
};

export default App;
