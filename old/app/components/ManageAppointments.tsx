import { FC, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAtom } from 'jotai';
import moment from 'moment';
import 'moment-timezone';
import toast from 'react-hot-toast';
import request from '@/app/api/request';
import {
  appointmentServicesState,
  appointmentState,
  formDataState,
  isRescheduledState,
} from '../atoms';
import removeHtmlTags from '../lib/removeHtmlTags';
import Preloader from './atoms/Preloader';

type data = {
  email: string | null;
  phone: string | null;
};

interface ManageAppointmentsProps {
  data: data;
}

const ManageAppointments: FC<ManageAppointmentsProps> = ({ data }) => {
  const router = useRouter();

  // Local variables
  const [appointments, setAppointments] = useState([]);
  const [cancelId, setCancelId] = useState<null | number>(null);

  // Global variables
  const [, setServicesIds] = useAtom(appointmentServicesState);
  const [loading, setLoading] = useState(false);
  const [, setFormData] = useAtom(formDataState);
  const [, setAppointment] = useAtom(appointmentState);
  const [, setIsRescheduled] = useAtom(isRescheduledState);

  useEffect(() => {
    setLoading(true);
    request(`/appointments?email=${data.email}&phone=${data.phone}`)
      .then((response: any) => {
        const { appointments, patient } = response;
        setFormData({
          firstName: patient.firstName,
          lastName: patient.lastName,
          email: patient.email,
          phone: patient.phoneMobile,
          id: patient.id,
        });
        const ap = appointments.filter(
          ({ appointment }: any) =>
            moment
              .tz(appointment.startsAt, 'Europe/Amsterdam')
              .isAfter(moment.tz(moment(), 'Europe/Amsterdam')) &&
            !appointment.canceledWhy,
        );
        setAppointments(ap);
      })
      .catch((error) => {
        // toast.error('Sie haben keine kommenden Termine');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const handleSelectAppointment = (appointment: any) => {
    setServicesIds(appointment.services);
    setAppointment(appointment);
    setIsRescheduled(true);
    router.push('/');
  };

  const cancelAppointment = async (appointment: any) => {
    try {
      setLoading(true);
      await request(`/update-appointments/${appointment.id}`, {
        method: 'PUT',
        // @ts-ignore
        body: {
          startsAt: appointment.startsAt,
          endsAt: appointment.endsAt,
          status: 'cancel',
        },
      });
      router.push('/cancelled');
    } catch (error) {
      toast.error('Ein Fehler ist aufgetreten, bitte versuchen Sie es erneut');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (appointment: any) => {
    if (appointment) {
      setCancelId(appointment.id);
    } else {
      setCancelId(null);
    }
  };
  const gotoHome = () => {
    setIsRescheduled(false);
    window.location.href = '/';
  };

  return (
    <div className="relative">
      <div className="appointments overflow-y-auto h-[96vh] lg:h-[62vh] scrollbar pb-16">
        <div className="grid grid-cols-1 gap-3">
          {appointments.length ? (
            appointments.map(({ appointment }: any) => (
              <div
                key={appointment.id}
                className="border shadow p-4 rounded-md flex justify-between items-center flex-col gap-4 overflow-hidden md:flex-row"
              >
                <div>
                  <h3 className="font-semibold">
                    {removeHtmlTags(appointment.title)}
                  </h3>
                  <p>
                    <strong>Datum:</strong>{" "}
                    {moment(appointment.startsAt)
                      .locale("de")
                      .format("Do MMMM YYYY")}{" "}
                  </p>
                  <p>
                    <strong>Uhrzeit:</strong>{" "}
                    {moment
                      .tz(appointment.startsAt, "Europe/Amsterdam")
                      .format("HH:mm")}{" "}
                    Uhr
                  </p>
                </div>
                <div className="">
                  <div
                    className={`flex gap-1 ${
                      cancelId === appointment.id ? "hidden" : "flex"
                    }`}
                  >
                    <button
                      className="action continue"
                      onClick={() => handleSelectAppointment(appointment)}
                    >
                      Verschieben
                    </button>
                    <button
                      className="action !border-2 !border-red !text-red"
                      onClick={() => handleDelete(appointment)}
                    >
                      Stornieren
                    </button>
                  </div>
                  <div
                    className={`duration-300 transition-opacity ${
                      cancelId === appointment.id
                        ? "opacity-100 w-auto h-auto"
                        : "opacity-0 invisible w-0 h-0"
                    }`}
                  >
                    <h3 className="font-semibold text-center">
                      Sind Sie sicher?
                    </h3>
                    <div className="flex gap-2 mt-2">
                      <button
                        className="border px-6 py-1 rounded-md bg-red text-white"
                        onClick={() => cancelAppointment(appointment)}
                      >
                        Ja
                      </button>
                      <button
                        className="continue action !px-8"
                        onClick={handleDelete}
                      >
                        Nein
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <>
              {!loading && (
                <p className="w-full mt-20 text-center">
                  Sie haben noch keine Termine.
                </p>
              )}
            </>
          )}
          {!loading && (
            <button
              className="continue action !w-fit mx-auto"
              onClick={gotoHome}
            >
              Termin buchen
            </button>
          )}
        </div>
        <Preloader isLoading={loading} />
      </div>
    </div>
  );
};

export default ManageAppointments;
