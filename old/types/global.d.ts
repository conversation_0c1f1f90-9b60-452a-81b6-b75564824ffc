interface Appointment {
  id: string;
  date: string;
  user: number;
  service: number;
  location: null | string;
  resources: number[];
}

interface Service {
  id: number | string;
  name: string;
  externalName: string;
  duration: number;
  gross?: number;
  description?: string;
  // price?: number | string;
  ac_active_value: number;
}

interface Category {
  id: number | string;
  name: string;
  services: Service[];
}

interface Slot {
  id: string;
  date: string;
  service: number | string;
  location?: string;
  [key]: any;
}

declare global {
  interface Window {
    fbq: any;
  }
}
