{"name": "booking-widget", "version": "0.0.1", "private": true, "scripts": {"dev": "clear && next dev --turbopack", "build": "clear && next build", "start": "clear && next start", "lint": "clear && next lint", "deploy": "clear && opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "clear && opennextjs-cloudflare build && opennextjs-cloudflare preview", "cftype": "clear && wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "db:generate": "clear && drizzle-kit generate", "db:push": "clear && drizzle-kit push", "db:sync": "pnpm db:generate && pnpm db:push"}, "dependencies": {"@better-fetch/fetch": "^1.1.18", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@neondatabase/serverless": "^1.0.1", "@opennextjs/cloudflare": "^1.4.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "moment-timezone": "^0.6.0", "next": "15.3.5", "next-themes": "^0.4.6", "qs": "^6.14.0", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "resend": "^4.7.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.12", "@types/qs": "^6.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "drizzle-kit": "^0.31.4", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "wrangler": "^4.24.1"}}