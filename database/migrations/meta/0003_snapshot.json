{"id": "d6169696-bcc0-4629-bef5-b41d735c6af1", "prevId": "765d7b6e-51d0-4b30-849c-d157a6b77315", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "cc_appointment_id": {"name": "cc_appointment_id", "type": "integer", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "service_ids": {"name": "service_ids", "type": "integer[]", "primaryKey": false, "notNull": true}, "slot_date": {"name": "slot_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 1800}, "status": {"name": "status", "type": "appointment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "resources": {"name": "resources", "type": "integer[]", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"appointments_patient_id_patients_id_fk": {"name": "appointments_patient_id_patients_id_fk", "tableFrom": "appointments", "tableTo": "patients", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointments_user_id_users_id_fk": {"name": "appointments_user_id_users_id_fk", "tableFrom": "appointments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"appointments_cc_appointment_id_unique": {"name": "appointments_cc_appointment_id_unique", "nullsNotDistinct": false, "columns": ["cc_appointment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.booking_sessions": {"name": "booking_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "selected_service_ids": {"name": "selected_service_ids", "type": "integer[]", "primaryKey": false, "notNull": false}, "selected_slot_id": {"name": "selected_slot_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "patient_data": {"name": "patient_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "booking_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"booking_sessions_session_token_unique": {"name": "booking_sessions_session_token_unique", "nullsNotDistinct": false, "columns": ["session_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.patients": {"name": "patients", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"patients_cc_id_unique": {"name": "patients_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}, "patients_email_unique": {"name": "patients_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_groups": {"name": "product_groups", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "hidden": {"name": "hidden", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_groups_name_unique": {"name": "product_groups_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.services": {"name": "services", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": true}, "group_id": {"name": "group_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 1800}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_description": {"name": "display_description", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "hidden": {"name": "hidden", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "users": {"name": "users", "type": "<PERSON><PERSON><PERSON>(255)[]", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"services_group_id_product_groups_id_fk": {"name": "services_group_id_product_groups_id_fk", "tableFrom": "services", "tableTo": "product_groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"services_cc_id_unique": {"name": "services_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.settings": {"name": "settings", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.time_slots": {"name": "time_slots", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "cc_slot_id": {"name": "cc_slot_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "available": {"name": "available", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 1800}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "resources": {"name": "resources", "type": "integer[]", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.two_factors": {"name": "two_factors", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "secret": {"name": "secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "backup_codes": {"name": "backup_codes", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"two_factors_user_id_users_id_fk": {"name": "two_factors_user_id_users_id_fk", "tableFrom": "two_factors", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "two_factor_enabled": {"name": "two_factor_enabled", "type": "boolean", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_username": {"name": "display_username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verifications": {"name": "verifications", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.appointment_status": {"name": "appointment_status", "schema": "public", "values": ["pending", "confirmed", "cancelled", "completed", "no_show"]}, "public.booking_status": {"name": "booking_status", "schema": "public", "values": ["draft", "pending", "confirmed", "cancelled"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["admin", "user"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}