CREATE TYPE "public"."appointment_status" AS ENUM('pending', 'confirmed', 'cancelled', 'completed', 'no_show');--> statement-breakpoint
CREATE TYPE "public"."booking_status" AS ENUM('draft', 'pending', 'confirmed', 'cancelled');--> statement-breakpoint
CREATE TYPE "public"."user_role" AS ENUM('admin', 'user');--> statement-breakpoint
CREATE TABLE "appointments" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"cc_appointment_id" integer,
	"patient_id" varchar(255),
	"user_id" varchar(255),
	"service_ids" integer[] NOT NULL,
	"slot_date" timestamp NOT NULL,
	"duration" integer DEFAULT 1800 NOT NULL,
	"status" "appointment_status" DEFAULT 'pending' NOT NULL,
	"notes" text,
	"location" varchar(255),
	"resources" integer[],
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "appointments_cc_appointment_id_unique" UNIQUE("cc_appointment_id")
);
--> statement-breakpoint
CREATE TABLE "booking_sessions" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"session_token" varchar(255) NOT NULL,
	"selected_service_ids" integer[],
	"selected_slot_id" varchar(255),
	"patient_data" jsonb,
	"status" "booking_status" DEFAULT 'draft' NOT NULL,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "booking_sessions_session_token_unique" UNIQUE("session_token")
);
--> statement-breakpoint
CREATE TABLE "time_slots" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"cc_slot_id" varchar(255),
	"service_id" integer NOT NULL,
	"date" timestamp NOT NULL,
	"available" boolean DEFAULT true NOT NULL,
	"duration" integer DEFAULT 1800 NOT NULL,
	"location" varchar(255),
	"resources" integer[],
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "role" "user_role" DEFAULT 'user' NOT NULL;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_patient_id_patients_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;