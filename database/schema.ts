import { relations } from 'drizzle-orm';
import { pgTable, timestamp, boolean, integer, varchar, jsonb, text, pgEnum } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

// Enums for type safety
export const userRoleEnum = pgEnum('user_role', ['admin', 'user']);
export const appointmentStatusEnum = pgEnum('appointment_status', ['pending', 'confirmed', 'cancelled', 'completed', 'no_show']);
export const bookingStatusEnum = pgEnum('booking_status', ['draft', 'pending', 'confirmed', 'cancelled']);

export const patients = pgTable('patients', {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    ccID: integer('cc_id').notNull().unique(),
    firstName: varchar('first_name', { length: 255 }).notNull(),
    lastName: varchar('last_name', { length: 255 }).notNull(),
    email: varchar('email', { length: 255 }).notNull().unique(),
    phone: varchar('phone', { length: 255 }).notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
    deletedAt: timestamp('deleted_at').notNull().defaultNow(),
    isDeleted: boolean('is_deleted').notNull().default(false),
});

/**
 * User table for authentication (Better Auth) with role-based access control
 * @see https://github.com/better-auth/better-auth/blob/main/packages/cli/test/__snapshots__/auth-schema.txt
 */
export const users = pgTable("users", {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    name: varchar("name", { length: 255 }).notNull(),
    email: varchar("email", { length: 255 }).notNull().unique(),
    emailVerified: boolean("email_verified").default(false).notNull(),
    image: varchar("image", { length: 255 }),
    role: userRoleEnum("role").default('user').notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
    twoFactorEnabled: boolean("two_factor_enabled"),
    username: varchar("username", { length: 255 }).unique(),
    displayUsername: varchar("display_username", { length: 255 }),
});

/**
 * Session table for authentication (Better Auth)
 */
export const sessions = pgTable("sessions", {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    expiresAt: timestamp("expires_at").notNull(),
    token: varchar("token", { length: 255 }).notNull().unique(),
    createdAt: timestamp("created_at").notNull(),
    updatedAt: timestamp("updated_at").notNull(),
    ipAddress: varchar("ip_address", { length: 255 }),
    userAgent: varchar("user_agent", { length: 255 }),
    userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Account table for OAuth and password providers (Better Auth)
 */
export const accounts = pgTable("accounts", {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    accountId: varchar("account_id", { length: 255 }).notNull(),
    providerId: varchar("provider_id", { length: 255 }).notNull(),
    userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
    accessToken: varchar("access_token", { length: 255 }),
    refreshToken: varchar("refresh_token", { length: 255 }),
    idToken: varchar("id_token", { length: 255 }),
    accessTokenExpiresAt: timestamp("access_token_expires_at"),
    refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
    scope: varchar("scope", { length: 255 }),
    password: varchar("password", { length: 255 }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
});

/**
 * Verification table for email/phone verification (Better Auth)
 */
export const verifications = pgTable("verifications", {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    identifier: varchar("identifier", { length: 255 }).notNull(),
    value: varchar("value", { length: 255 }).notNull(),
    expiresAt: timestamp("expires_at").notNull(),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at").defaultNow().$onUpdate(() => new Date()),
});

/**
 * Two-factor authentication table (Better Auth)
 */
export const twoFactors = pgTable("two_factors", {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    secret: varchar("secret", { length: 255 }).notNull(),
    backupCodes: varchar("backup_codes", { length: 255 }).notNull(),
    userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
});

export const authSchema = {
    users,
    sessions,
    accounts,
    verifications,
    twoFactors,
};

// Booking-specific tables
export const appointments = pgTable('appointments', {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    ccAppointmentId: integer('cc_appointment_id').unique(),
    patientId: varchar('patient_id', { length: 255 }).references(() => patients.id, { onDelete: "cascade" }),
    userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: "set null" }),
    serviceIds: integer('service_ids').array().notNull(),
    slotDate: timestamp('slot_date').notNull(),
    duration: integer('duration').notNull().default(1800), // in seconds
    status: appointmentStatusEnum('status').default('pending').notNull(),
    notes: text('notes'),
    location: varchar('location', { length: 255 }),
    resources: integer('resources').array(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
});

export const timeSlots = pgTable('time_slots', {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    ccSlotId: varchar('cc_slot_id', { length: 255 }),
    serviceId: integer('service_id').notNull(),
    date: timestamp('date').notNull(),
    available: boolean('available').default(true).notNull(),
    duration: integer('duration').notNull().default(1800),
    location: varchar('location', { length: 255 }),
    resources: integer('resources').array(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
});

export const bookingSessions = pgTable('booking_sessions', {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    sessionToken: varchar('session_token', { length: 255 }).notNull().unique(),
    selectedServiceIds: integer('selected_service_ids').array(),
    selectedSlotId: varchar('selected_slot_id', { length: 255 }),
    patientData: jsonb('patient_data'),
    status: bookingStatusEnum('status').default('draft').notNull(),
    expiresAt: timestamp('expires_at').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
});

export const productGroups = pgTable('product_groups', {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    ccID: integer('cc_id').notNull(),
    name: varchar('name', { length: 255 }).unique(),
    order: integer('order').default(0),
    displayName: varchar('display_name', { length: 255 }),
    displayOrder: integer('display_order').default(0),
    hidden: boolean('hidden').default(false),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
});

export const services = pgTable('services', {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    ccID: integer('cc_id').notNull().unique(),
    groupId: varchar('group_id', { length: 255 }).references(() => productGroups.id, { onDelete: "cascade" }),
    name: varchar('name', { length: 255 }).notNull(),
    duration: integer('duration').notNull().default(1800),
    price: integer('price').notNull().default(0),
    description: text('description'),

    displayName: varchar('display_name', { length: 255 }),
    displayDescription: text('display_description'),
    displayOrder: integer('display_order').default(0),
    image: varchar('image', { length: 255 }),
    hidden: boolean('hidden').default(false),

    users: varchar('users', { length: 255 }).array(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
})

// Relations
export const groupServices = relations(productGroups, ({ many }) => ({
    services: many(services),
}))

export const serviceGroup = relations(services, ({ one }) => ({
    group: one(productGroups, {
        fields: [services.groupId],
        references: [productGroups.id],
    }),
}))

export const userAppointments = relations(users, ({ many }) => ({
    appointments: many(appointments),
}))

export const patientAppointments = relations(patients, ({ many }) => ({
    appointments: many(appointments),
}))

export const appointmentPatient = relations(appointments, ({ one }) => ({
    patient: one(patients, {
        fields: [appointments.patientId],
        references: [patients.id],
    }),
    user: one(users, {
        fields: [appointments.userId],
        references: [users.id],
    }),
}))

export const settings = pgTable('settings', {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    key: varchar('key', { length: 255 }).notNull(),
    value: varchar('value', { length: 255 }).notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
});

// Zod schemas for validation
export const insertProductGroupSchema = createInsertSchema(productGroups);
export const selectProductGroupSchema = createSelectSchema(productGroups);

export const insertServiceSchema = createInsertSchema(services);
export const selectServiceSchema = createSelectSchema(services);

export const insertSettingSchema = createInsertSchema(settings);
export const selectSettingSchema = createSelectSchema(settings);

export const insertUserSchema = createInsertSchema(users);
export const selectUserSchema = createSelectSchema(users);

export const insertPatientSchema = createInsertSchema(patients);
export const selectPatientSchema = createSelectSchema(patients);

export const insertAppointmentSchema = createInsertSchema(appointments);
export const selectAppointmentSchema = createSelectSchema(appointments);

export const insertTimeSlotSchema = createInsertSchema(timeSlots);
export const selectTimeSlotSchema = createSelectSchema(timeSlots);

export const insertBookingSessionSchema = createInsertSchema(bookingSessions);
export const selectBookingSessionSchema = createSelectSchema(bookingSessions);