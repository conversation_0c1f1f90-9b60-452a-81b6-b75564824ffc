/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "wolanin-booking-widget",
	"main": ".open-next/worker.js",
	"compatibility_date": "2025-03-01",
	"compatibility_flags": [
		"nodejs_compat",
		"global_fetch_strictly_public"
	],
	"assets": {
		"binding": "ASSETS",
		"directory": ".open-next/assets"
	},
	"observability": {
		"enabled": true
	},
	"r2_buckets": [
		{
			"bucket_name": "wolanin",
			"binding": "wolanin"
		}
	],
	"vars": {
		"ENV": "production",
		"DATABASE_URL": "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",
		"CC_API_TOKEN": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		"CC_PUBLIC_TOKEN": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		"RESEND_API_KEY": "re_bH8xGmsG_FZL21cTgcLttA6X38D1CQxyG",
		"CC_API_URL": "https://wolaninmd.clinicore.eu/api/v1",
		"BETTER_AUTH_SECRET": "KV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6IjkzNzkyMDU1NmEyMzczNGVlZmE0OTNlMmFmMWJhY2M5N",
		"BETTER_AUTH_URL": "http://localhost:3000"
	}
}