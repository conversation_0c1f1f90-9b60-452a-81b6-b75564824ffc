import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";

/**
 * State for Group slice.
 */
export interface GroupState {
    groups: IProductGroupWithServices[];
    loading: boolean;
    error: string | null;
    editGroupStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    editGroupError: string | null;
    deleteGroupStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    deleteGroupError: string | null;
    alterServiceGroupPositionStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    alterServiceGroupPositionError: string | null;
}

const initialState: GroupState = {
    groups: [],
    loading: false,
    error: null,
    editGroupStatus: 'idle',
    editGroupError: null,
    deleteGroupStatus: 'idle',
    deleteGroupError: null,
    alterServiceGroupPositionStatus: 'idle',
    alterServiceGroupPositionError: null,
};

/**
 * Async thunk to fetch all groups and their services.
 */
export const fetchGroups = createAsyncThunk<IProductGroupWithServices[], void, { rejectValue: string }>(
    "group/fetchGroups",
    async (_, { rejectWithValue }) => {
        try {
            const res = await fetch("/api/groups");
            if (!res.ok) throw new Error("Failed to fetch groups");
            const data = await res.json() as IProductGroupWithServices[];
            return data;
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to edit a group.
 */
export const editGroup = createAsyncThunk<
    void,
    { id: string; displayName: string },
    { rejectValue: string }
>(
    "group/editGroup",
    async ({ id, displayName }, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/groups", {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id, displayName }),
            });
            if (!res.ok) throw new Error("Failed to edit group");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to delete (hide) a group.
 */
export const deleteGroup = createAsyncThunk<
    void,
    { id: string },
    { rejectValue: string }
>(
    "group/deleteGroup",
    async ({ id }, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/groups", {
                method: "DELETE",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id }),
            });
            if (!res.ok) throw new Error("Failed to delete group");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to alter the position of a group.
 */
export const alterServiceGroupPosition = createAsyncThunk<
    void,
    { id: string; displayOrder: number },
    { rejectValue: string }
>(
    "group/alterServiceGroupPosition",
    async ({ id, displayOrder }, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/groups", {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id, displayOrder }),
            });
            if (!res.ok) throw new Error("Failed to alter group position");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

export const groupSlice = createSlice({
    name: "group",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchGroups.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchGroups.fulfilled, (state, action: PayloadAction<IProductGroupWithServices[]>) => {
                state.groups = action.payload;
                state.loading = false;
            })
            .addCase(fetchGroups.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "Failed to fetch groups";
            })
            // editGroup
            .addCase(editGroup.pending, (state) => {
                state.editGroupStatus = 'pending';
                state.editGroupError = null;
            })
            .addCase(editGroup.fulfilled, (state) => {
                state.editGroupStatus = 'succeeded';
            })
            .addCase(editGroup.rejected, (state, action) => {
                state.editGroupStatus = 'failed';
                state.editGroupError = action.payload || 'Failed to edit group';
            })
            // deleteGroup
            .addCase(deleteGroup.pending, (state) => {
                state.deleteGroupStatus = 'pending';
                state.deleteGroupError = null;
            })
            .addCase(deleteGroup.fulfilled, (state) => {
                state.deleteGroupStatus = 'succeeded';
            })
            .addCase(deleteGroup.rejected, (state, action) => {
                state.deleteGroupStatus = 'failed';
                state.deleteGroupError = action.payload || 'Failed to delete group';
            })
            // alterServiceGroupPosition
            .addCase(alterServiceGroupPosition.pending, (state) => {
                state.alterServiceGroupPositionStatus = 'pending';
                state.alterServiceGroupPositionError = null;
            })
            .addCase(alterServiceGroupPosition.fulfilled, (state) => {
                state.alterServiceGroupPositionStatus = 'succeeded';
            })
            .addCase(alterServiceGroupPosition.rejected, (state, action) => {
                state.alterServiceGroupPositionStatus = 'failed';
                state.alterServiceGroupPositionError = action.payload || 'Failed to alter group position';
            });
    },
});

export default groupSlice.reducer; 