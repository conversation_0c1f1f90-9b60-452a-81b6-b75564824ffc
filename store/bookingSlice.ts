import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  BookingState,
  Service,
  TimeSlot,
  PatientFormData,
  Category,
  ServicesResponse,
  BookingResponse
} from "@/types/booking";

/**
 * Initial state for the booking slice
 */
const initialState: BookingState = {
  currentStep: 'services',
  selectedServices: [],
  selectedSlot: null,
  patientData: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    message: '',
  },
  totalDuration: 0,
  totalPrice: 0,
  isLoading: false,
  error: null,
  categories: [],
  hasLoadedServices: false,
};

/**
 * Async thunk to fetch available services for booking
 */
export const fetchBookingServices = createAsyncThunk<
  Category[],
  void,
  { rejectValue: string }
>(
  "booking/fetchServices",
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch("/api/booking/services");
      if (!response.ok) {
        throw new Error("Failed to fetch services");
      }
      const data: ServicesResponse = await response.json();
      return data.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Unknown error");
    }
  }
);

// Note: Time slot fetching is handled by the slotsSlice.ts
// This booking slice focuses on the overall booking flow and form data

/**
 * Async thunk to create a booking
 */
export const createBooking = createAsyncThunk<
  any,
  {
    serviceIds: number[];
    slotId: string;
    patientData: PatientFormData;
    slot: TimeSlot;
  },
  { rejectValue: string }
>(
  "booking/createBooking",
  async ({ serviceIds, slotId, patientData, slot }, { rejectWithValue }) => {
    try {
      const response = await fetch("/api/booking/book", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          services: serviceIds,
          slot: slot.date,
          duration: slot.duration,
          ...patientData,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create booking");
      }

      const data: BookingResponse = await response.json();
      return data.appointment;
    } catch (error: any) {
      return rejectWithValue(error.message || "Unknown error");
    }
  }
);

/**
 * Booking slice
 */
export const bookingSlice = createSlice({
  name: "booking",
  initialState,
  reducers: {
    // Navigation actions
    setCurrentStep: (state, action: PayloadAction<BookingState['currentStep']>) => {
      state.currentStep = action.payload;
    },
    
    nextStep: (state) => {
      const steps: BookingState['currentStep'][] = ['services', 'calendar', 'form', 'confirmation', 'success'];
      const currentIndex = steps.indexOf(state.currentStep);
      if (currentIndex < steps.length - 1) {
        state.currentStep = steps[currentIndex + 1];
      }
    },
    
    previousStep: (state) => {
      const steps: BookingState['currentStep'][] = ['services', 'calendar', 'form', 'confirmation', 'success'];
      const currentIndex = steps.indexOf(state.currentStep);
      if (currentIndex > 0) {
        state.currentStep = steps[currentIndex - 1];
      }
    },

    // Service selection actions
    addService: (state, action: PayloadAction<Service>) => {
      const service = action.payload;
      const existingIndex = state.selectedServices.findIndex(s => s.id === service.id);
      
      if (existingIndex === -1) {
        state.selectedServices.push({
          ...service,
          ac_active_value: state.selectedServices.length + 1
        });
        state.totalDuration += service.duration;
        state.totalPrice += service.price;
      }
    },
    
    removeService: (state, action: PayloadAction<string | number>) => {
      const serviceId = action.payload;
      const serviceIndex = state.selectedServices.findIndex(s => s.id === serviceId);
      
      if (serviceIndex !== -1) {
        const service = state.selectedServices[serviceIndex];
        state.totalDuration -= service.duration;
        state.totalPrice -= service.price;
        state.selectedServices.splice(serviceIndex, 1);
        
        // Update ac_active_value for remaining services
        state.selectedServices.forEach((s, index) => {
          s.ac_active_value = index + 1;
        });
      }
    },
    
    clearServices: (state) => {
      state.selectedServices = [];
      state.totalDuration = 0;
      state.totalPrice = 0;
    },

    // Slot selection actions
    setSelectedSlot: (state, action: PayloadAction<TimeSlot | null>) => {
      state.selectedSlot = action.payload;
    },

    // Patient data actions
    setPatientData: (state, action: PayloadAction<Partial<PatientFormData>>) => {
      state.patientData = { ...state.patientData, ...action.payload };
    },
    
    clearPatientData: (state) => {
      state.patientData = {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        message: '',
      };
    },

    // Error handling
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },

    // Reset booking state
    resetBooking: (state) => {
      return { ...initialState };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch services
      .addCase(fetchBookingServices.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBookingServices.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload;
        state.hasLoadedServices = true;
      })
      .addCase(fetchBookingServices.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || "Failed to fetch services";
      })
      
      // Note: Time slot fetching is handled by slotsSlice
      
      // Create booking
      .addCase(createBooking.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createBooking.fulfilled, (state) => {
        state.isLoading = false;
        state.currentStep = 'success';
      })
      .addCase(createBooking.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || "Failed to create booking";
      });
  },
});

export const {
  setCurrentStep,
  nextStep,
  previousStep,
  addService,
  removeService,
  clearServices,
  setSelectedSlot,
  setPatientData,
  clearPatientData,
  setError,
  clearError,
  resetBooking,
} = bookingSlice.actions;

export default bookingSlice.reducer;
