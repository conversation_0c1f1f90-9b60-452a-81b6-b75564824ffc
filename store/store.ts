import { configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import authReducer from "./authSlice";
import servicesReducer from "./servicesSlice";
import groupReducer from "./groupSlice";
import bookingReducer from "./bookingSlice";
import appointmentManagementReducer from "./appointmentManagementSlice";
import appointmentBookingReducer from "./appointmentBookingSlice";
import slotsReducer from "./slotsSlice";

/**
 * Configures the Redux store for the application.
 *
 * @returns {ReturnType<typeof configureStore>} The configured Redux store instance.
 */
export const store = configureStore({
  reducer: {
    auth: authReducer,
    services: servicesReducer,
    group: groupReducer,
    booking: bookingReducer,
    appointmentManagement: appointmentManagementReducer,
    appointmentBooking: appointmentBookingReducer,
    slots: slotsReducer,
  },
});

/**
 * Type for the root state of the Redux store.
 */
export type RootState = ReturnType<typeof store.getState>;

/**
 * Type for the Redux dispatch function.
 */
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();
