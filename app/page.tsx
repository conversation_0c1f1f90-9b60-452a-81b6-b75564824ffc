"use client"

import { ServiceSelection } from "@/components/service-selection"
import { ServicesSummary } from "@/components/services-summary"
import { DateTimeSelection } from "@/components/date-time-selection"
import { ContactForm } from "@/components/contact-form"
import { AppointmentConfirmation } from "@/components/appointment-confirmation"
import { useAppSelector } from "@/store"

export type Service = {
  id: string
  name: string
  externalName: string
  duration: number
  price: string
  gross: number
  groupNames?: string[]
  productGroup?: string
  selected?: boolean
}

export type AppointmentData = {
  services: Service[]
  date: string
  time: string
  contact: {
    firstName: string
    lastName: string
    phone: string
    email: string
    additionalInfo: string
  }
}

export default function AppointmentBooking() {
  const currentStep = useAppSelector((state) => state.appointmentBooking.currentStep)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="h-[800px]">
          {currentStep === 1 && <ServiceSelection />}
          {currentStep === 2 && <ServicesSummary />}
          {currentStep === 3 && <DateTimeSelection />}
          {currentStep === 4 && <ContactForm />}
          {currentStep === 5 && <AppointmentConfirmation />}
        </div>
      </div>
    </div>
  )
}
