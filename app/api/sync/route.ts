import { with<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/requestHandler"
import { ccRequest } from "@/utils/ccRequest"
import { getEnv } from "@/utils/ctx"
import { getDB, dbSchema } from "@database"
import { sql } from "drizzle-orm"

/**
 * GET handler for /api/sync
 * Returns grouped online services with product group info in the required format.
 */
export const GET = withAuthHandler(async () => {
    // Fetch product groups and online services in parallel
    const [productGroupsRes, onlineServices] = await Promise.all([
        getProductGroups(),
        getPublicServices()
    ])
    const productGroups: IProductGroup[] = productGroupsRes;

    // Group online services by group name
    const groupNameMap: Record<string, IPublicService[]> = {};
    onlineServices.forEach((service) => {
        if (service.groupNames) {
            service.groupNames.forEach((gName: string) => {
                if (!groupNameMap[gName]) groupNameMap[gName] = [];
                groupNameMap[gName].push(service);
            });
        }
    });

    // Prepare newData array for grouped services
    const newData = Object.keys(groupNameMap)
        .filter((key) => key.trim().length)
        .map((key) => {
            const group = productGroups.find(pg => pg.name === key);
            return {
                name: key,
                id: group ? group.id : 0,
                services: groupNameMap[key].map((service) => ({
                    id: service.id,
                    duration: service.duration / 60,
                    price: parseFloat(service.price as string),
                    name: service.name,
                    description: stripHtml(service.description),
                    users: service.users,
                })),
            };
        });

    // Open DB connection once
    const db = await getDB();

    // Upsert product groups first
    const upsertedGroups = await upsertGroups(
        newData.map((d) => ({ name: d.name, id: d.id })),
        db
    );

    /**
     * Map group name to upserted group id, skipping any group with missing id or name.
     */
    const groupNameToId: Record<string, string> = {};
    upsertedGroups.forEach((g) => {
        if (g && g.name && g.id) {
            groupNameToId[g.name] = g.id;
        }
    });

    // Prepare services data with correct groupId
    const servicesData = newData.flatMap((d) => {
        const groupId = groupNameToId[d.name];
        if (!groupId) {
            throw new Error(`No groupId found for group name: ${d.name}`);
        }
        return d.services.map((s) => ({
            ...s,
            groupId,
        }));
    });

    // Upsert services
    await upsertServices(servicesData, db);

    return Response.json({
        status: 200,
        message: "OK",
    });
})

/**
 * Removes all HTML tags from a string.
 * @param {string} html - The HTML string to sanitize.
 * @returns {string} The plain text string with HTML tags removed.
 */
function stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, "");
}

/**
 * Fetches public (online) services from the external API.
 * @returns {Promise<IPublicService[]>}
 */
const getPublicServices = async (): Promise<IPublicService[]> => {
    const env = await getEnv()
    const requestOptions = {
        method: "GET",
        headers: {
            "Authorization": `Bearer ${env.CC_PUBLIC_TOKEN}`,
            "Accept-Encoding": "application/json",
            "Content-Type": "application/json",
        },
    }
    const response = await fetch(`https://scheduler.clinicore.eu/api/scheduler/services`, requestOptions)
    const data: IPublicService[] = (await response.json() as { services: IPublicService[] }).services
    return data
}

/**
 * Fetches product groups from the internal API.
 * @returns {Promise<IProductGroup[]>}
 */
const getProductGroups = async (): Promise<IProductGroup[]> => {
    const productGroups = await ccRequest<{ productGroups: IProductGroup[] }>("/productGroups", "GET");
    return productGroups.productGroups;
}

/**
 * Upserts product groups into the database.
 * @param data Array of group objects to upsert.
 * @param db Database connection.
 */
const upsertGroups = async (data: { name: string; id: number; }[], db: Awaited<ReturnType<typeof getDB>>) => {
    const upsert = await db.insert(dbSchema.productGroups).values(data.map((d) => ({ ccID: d.id, name: d.name })))
        .onConflictDoUpdate({
            target: [dbSchema.productGroups.name],
            set: {
                name: sql`excluded.name`,
                order: sql`excluded.order`,
            },
        }).returning()
    return upsert
}

/**
 * Upserts services into the database.
 * @param data Array of service objects to upsert.
 * @param db Database connection.
 */
const upsertServices = async (
    data: { name: string; id: number; duration: number; description: string; users: string[]; price: number; groupId: string }[],
    db: Awaited<ReturnType<typeof getDB>>
) => {
    const upsert = await db.insert(dbSchema.services).values(
        data.map((d) => ({
            ccID: d.id,
            name: d.name,
            duration: d.duration,
            description: d.description,
            users: d.users,
            price: d.price,
            groupId: d.groupId, // use camelCase for Drizzle schema
        }))
    ).onConflictDoUpdate({
        target: [dbSchema.services.ccID],
        set: {
            name: sql`excluded.name`,
            duration: sql`excluded.duration`,
            description: sql`excluded.description`,
            users: sql`excluded.users`,
            price: sql`excluded.price`,
            groupId: sql`excluded.group_id`,
        },
    }).returning();
    return upsert;
}