import { NextResponse } from "next/server";
import { ccRequest } from "@/lib/clinicore";
import { getEnv } from "@/utils/ctx";

/**
 * Type for a service object.
 */
interface Service {
  id: string;
  name: string;
  duration: number;
  price: string;
  groupNames?: string[];
  productGroup?: string;
  [key: string]: any;
}

/**
 * Type for a product group object.
 */
interface ProductGroup {
  id: string;
  name: string;
  services?: Service[];
}

/**
 * GET handler for fetching and grouping services from CliniCore.
 * @returns JSON response with grouped services
 */
export async function GET() {
  try {
    const env = await getEnv();

    const requestOptions = {
      method: "GET",
      headers: {
        Authorization: "Bearer " + env.CC_PUBLIC_TOKEN,
        "Accept-Encoding": "application/json",
        "Content-Type": "application/json",
      },
    };

    // Fetch product groups and online services in parallel
    const [productGroupsRes, onlineServicesRes] = await Promise.all([
      ccRequest("/productGroups", "GET"),
      fetch("https://scheduler.clinicore.eu/api/scheduler/services", requestOptions).then((res) => res.json()),
    ]);

    const productGroups: ProductGroup[] = (productGroupsRes as any)?.productGroups ?? [];
    const onlineServices: Service[] = (onlineServicesRes as any)?.services ?? [];

    // Group online services by group name
    const groupNameMap: Record<string, Service[]> = {};
    onlineServices.forEach((service) => {
      if (service.groupNames) {
        service.groupNames.forEach((gName: string) => {
          if (!groupNameMap[gName]) groupNameMap[gName] = [];
          groupNameMap[gName].push(service);
        });
      }
    });

    // Prepare newData array for grouped services
    const newData = Object.keys(groupNameMap)
      .filter((key) => key.trim().length)
      .map((key) => {
        const group = productGroups.find(pg => pg.name === key);
        return {
          name: key,
          id: group ? group.id : undefined, // use group id if found
          services: groupNameMap[key].map((service) => ({
            ...service,
            duration: service.duration / 60,
            gross: parseFloat(service.price),
            externalName: service.name,
          })),
        };
      });

    return NextResponse.json({
      status: 200,
      message: "OK",
      groups: newData
    });
  } catch (error: any) {
    console.error('Error fetching services:', error);

    return NextResponse.json({
      status: 500,
      message: "Fehler beim Laden der Services. Bitte versuchen Sie es später noch einmal.",
      error: error?.message || error,
    }, { status: 500 });
  }
}
