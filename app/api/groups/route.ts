export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

import { getDB, dbSchema } from "@/database";
import { asc, eq } from "drizzle-orm"
import { NextResponse } from "next/server";

interface RequestBody {
    id: string;
    displayName?: string;
    displayOrder?: number;
}

/**
 * API handler to fetch product groups and their services, ordered by displayOrder.
 * @param req Next.js request object
 * @returns JSON response with groups and their services
 */
export async function GET() {
    const db = await getDB();
    const groups = await db.query.productGroups.findMany({
        with: {
            services: {
                orderBy: [asc(dbSchema.services.displayOrder)]
            }
        },
        orderBy: [asc(dbSchema.productGroups.displayOrder)]
    });
    return NextResponse.json(groups);
}

export const PUT = async (request: Request) => {
    const { id, displayName, displayOrder } = await request.json() as RequestBody;
    const db = await getDB();
    if (id && displayOrder !== undefined) {
        const result = await updateGroupPosition(id, displayOrder);
        return NextResponse.json({ message: "Group position updated", result });
    }
    if (id && (displayName !== undefined)) {
        await db.update(dbSchema.productGroups).set({ displayName }).where(eq(dbSchema.productGroups.id, id));
    }
    return NextResponse.json({ message: "Group updated" });
}

export const DELETE = async (request: Request) => {
    const { id } = await request.json() as RequestBody;
    const db = await getDB();
    const group = await db.query.productGroups.findFirst({
        where: eq(dbSchema.productGroups.id, id),
    });
    if (!group) {
        return NextResponse.json({ message: "Group not found" }, { status: 404 });
    }
    if (group.hidden) {
        await db.update(dbSchema.productGroups).set({ hidden: false }).where(eq(dbSchema.productGroups.id, id));
    } else {
        await db.update(dbSchema.productGroups).set({ hidden: true }).where(eq(dbSchema.productGroups.id, id));
    }
    return NextResponse.json({ message: `Group ${group.hidden ? "Re-activated" : "Deactivated"}` });
}


const updateGroupPosition = async (id: string, displayOrder: number): Promise<typeof dbSchema.productGroups.$inferSelect> => {
    const db = await getDB();
    const group = await db.query.productGroups.findFirst({
        where: eq(dbSchema.productGroups.id, id),
    });
    if (!group) {
        throw new Error("Group not found");
    }
    if (group.displayOrder && group.displayOrder === displayOrder) {
        return group;
    }

    if (group.displayOrder) {
        await db.update(dbSchema.productGroups).set({ displayOrder: group.displayOrder }).where(eq(dbSchema.productGroups.displayOrder, group.displayOrder));
    }
    await db.update(dbSchema.productGroups).set({ displayOrder }).where(eq(dbSchema.productGroups.id, id));
    return group;
}