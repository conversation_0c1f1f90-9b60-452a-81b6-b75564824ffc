import { NextResponse } from "next/server";
import { dbSchema, getDB } from "@/database";
import { and, eq } from "drizzle-orm";

interface RequestBody {
    id: string;
    hidden: boolean;
    displayName?: string;
    displayDescription?: string;
    duration?: number;
    price?: number;
    image?: string;
    displayOrder?: number;
}

export const PUT = async (request: Request) => {
    const { id, hidden, displayName, displayDescription, duration, price, image, displayOrder } = await request.json() as RequestBody;
    const db = await getDB();
    if (hidden && id) {
        await db.update(dbSchema.services).set({ hidden }).where(eq(dbSchema.services.id, id));
    }
    if (id && displayOrder !== undefined) {
        const result = await updateServicePosition(id, displayOrder);
        return NextResponse.json({ message: "Service position updated", result });
    }
    if (id && (displayName || displayDescription || duration || price || image)) {
        await db.update(dbSchema.services).set({ displayName, displayDescription, duration, price, image }).where(eq(dbSchema.services.id, id));
    }
    return NextResponse.json({ message: "Service updated" });
}

export const DELETE = async (request: Request) => {
    const { id } = await request.json() as RequestBody;
    const db = await getDB();
    const service = await db.query.services.findFirst({
        where: eq(dbSchema.services.id, id),
    });
    if (!service) {
        return NextResponse.json({ message: "Service not found" }, { status: 404 });
    }
    if (service.hidden) {
        await db.update(dbSchema.services).set({ hidden: false }).where(eq(dbSchema.services.id, id));
    } else {
        await db.update(dbSchema.services).set({ hidden: true }).where(eq(dbSchema.services.id, id));
    }
    return NextResponse.json({ message: `Service ${service.hidden ? "Re-activated" : "Deactivated"}` });
}




/**
 * Swaps the displayOrder between two services: the one being moved and the one currently at the target position.
 * @param {string} id - The ID of the service to move.
 * @param {number} displayOrder - The new displayOrder position for the service.
 * @returns {Promise<IStoreService>} The updated service after swapping positions.
 */
const updateServicePosition = async (
    id: string,
    newDisplayOrder: number
): Promise<IStoreService> => {
    const db = await getDB();

    // Find the service to move
    const serviceToMove = await db.query.services.findFirst({
        where: eq(dbSchema.services.id, id),
    });
    if (!serviceToMove) {
        throw new Error("Service not found");
    }

    const groupId = serviceToMove.groupId as string;

    const currentPosition = serviceToMove.displayOrder;

    // No-op if positions are the same
    if (currentPosition === newDisplayOrder) {
        return serviceToMove as IStoreService;
    }

    // Find the service currently at the target position in the same group
    const serviceAtTarget = await db.query.services.findFirst({
        where: and(
            eq(dbSchema.services.groupId, groupId),
            eq(dbSchema.services.displayOrder, newDisplayOrder)
        ),
    });

    // If service exists at target, update its position first
    if (serviceAtTarget && serviceAtTarget.id !== id) {
        await db.update(dbSchema.services)
            .set({ displayOrder: currentPosition })
            .where(eq(dbSchema.services.id, serviceAtTarget.id));
    }

    // Then update the moving service's displayOrder
    const [updatedService] = await db.update(dbSchema.services)
        .set({ displayOrder: newDisplayOrder })
        .where(eq(dbSchema.services.id, id))
        .returning();

    return updatedService as IStoreService;
};