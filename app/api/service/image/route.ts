import { NextResponse } from "next/server";
import { getEnv } from "@/utils/ctx";

/**
 * POST handler for uploading a service image to R2.
 * Saves the image with the service id as the file name (preserving extension).
 * Deletes any existing image for that id before saving the new one.
 * @param {Request} request - The incoming request with form data: file, id
 * @returns {Promise<Response>} JSON with upload result
 */
export const POST = async (request: Request): Promise<Response> => {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const id = formData.get("id") as string;
    const r2 = (await getEnv()).wolanin;

    // Generate the key using the id and file extension
    const ext = file.name.includes('.') ? file.name.substring(file.name.lastIndexOf('.')) : '';
    const key = `service/${id}${ext}`;

    // Check and delete old image if it exists
    const existing = await r2.get(key);
    if (existing) {
        try {
            await r2.delete(key);
        } catch {
            // Optionally log error, but don't block upload
        }
    }

    const buffer = await file.arrayBuffer();
    await r2.put(key, buffer, {
        httpMetadata: {
            contentType: file.type,
        },
    });
    // Return the key as the URL
    return NextResponse.json({ message: "Image uploaded", url: `/images/${key}` });
}