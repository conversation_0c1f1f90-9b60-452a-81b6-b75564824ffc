import { NextRequest, NextResponse } from "next/server";
import { getDB } from "@/database";
import { services, productGroups } from "@/database/schema";
import { eq, and } from "drizzle-orm";

/**
 * Service interface matching the expected response format
 */
interface Service {
  id: string;
  name: string;
  duration: number;
  price: string;
  gross: number;
  externalName: string;
  ccID: number;
}

/**
 * Product group interface for the response
 */
interface ProductGroup {
  id: string;
  name: string;
  services: Service[];
}

/**
 * GET handler for fetching booking services from local database
 * Returns services grouped by categories, respecting the hidden field
 * Matches the old API response format for frontend compatibility
 */
export async function GET(req: NextRequest) {
  try {
    const db = await getDB();

    // Fetch all non-hidden product groups with their services
    const groupsWithServices = await db
      .select({
        groupId: productGroups.id,
        groupName: productGroups.name,
        groupDisplayName: productGroups.displayName,
        groupOrder: productGroups.order,
        serviceId: services.id,
        serviceName: services.name,
        serviceCcId: services.ccID,
        serviceDuration: services.duration,
        servicePrice: services.price,
        serviceDisplayName: services.displayName,
        serviceDescription: services.description,
      })
      .from(productGroups)
      .leftJoin(
        services,
        and(
          eq(services.groupId, productGroups.id),
          eq(services.hidden, false) // Only include non-hidden services
        )
      )
      .where(eq(productGroups.hidden, false)) // Only include non-hidden groups
      .orderBy(productGroups.order, productGroups.name);

    // Group the results by product group
    const groupMap: Record<string, ProductGroup> = {};

    groupsWithServices.forEach((row) => {
      const groupId = row.groupId;
      const groupName = row.groupDisplayName || row.groupName || 'Unnamed Group';

      if (!groupMap[groupId]) {
        groupMap[groupId] = {
          id: groupId,
          name: groupName,
          services: []
        };
      }

      // Add service if it exists (leftJoin might return null services)
      if (row.serviceId && row.serviceCcId && row.serviceName && row.serviceDuration !== null && row.servicePrice !== null) {
        const service: Service = {
          id: row.serviceCcId.toString(), // Use CC ID for frontend compatibility
          name: row.serviceDisplayName || row.serviceName,
          duration: Math.round(row.serviceDuration / 60), // Convert seconds to minutes
          price: (row.servicePrice / 100).toFixed(2), // Convert cents to euros
          gross: row.servicePrice / 100, // Convert cents to euros
          externalName: row.serviceName,
          ccID: row.serviceCcId
        };

        groupMap[groupId].services.push(service);
      }
    });

    // Convert to array and filter out groups with no services
    const data = Object.values(groupMap).filter(group => group.services.length > 0);

    return NextResponse.json({
      status: 200,
      message: "OK",
      data: data
    });
  } catch (error: any) {
    console.error('Error fetching booking services from database:', error);

    return NextResponse.json({
      status: 500,
      message: "Fehler beim Laden der Services. Bitte versuchen Sie es später noch einmal.",
      error: error?.message || error,
    }, { status: 500 });
  }
}
