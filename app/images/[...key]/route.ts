import { getEnv } from "@/utils/ctx";

export async function GET(
    request: Request,
    { params }: { params: Promise<{ key: string[] }> }
) {
    const { key } = await params;
    const r2 = (await getEnv()).wolanin;
    const object = await r2.get(key.join("/"));
    if (!object || !object.body) {
        return new Response("Not found", { status: 404 });
    }
    const headers = new Headers();
    headers.set("Content-Type", object.httpMetadata?.contentType || "application/octet-stream");
    return new Response(object.body, { status: 200, headers });
}