import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import StoreProvider from "@/store/Provider";
import { ModalProvider } from "@/components/Modal";
import { ConfirmProvider } from "@/components/Confirm";
import { Toaster } from "@/components/ui/sonner";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Wolanin aesthetic",
  description: "Wolanin aesthetic",
};

/**
 * Root layout for the application. Wraps the app with Redux Provider.
 *
 * @param children - React children nodes
 */
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <StoreProvider>
          <ModalProvider>
            <ConfirmProvider>
              {children}
              <Toaster />
            </ConfirmProvider>
          </ModalProvider>
        </StoreProvider>
      </body>
    </html>
  );
}
