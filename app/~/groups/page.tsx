"use client"
import { useEffect, useState } from 'react';

const Groups = () => {
    const [groups, setGroups] = useState<IProductGroup[]>([])
    useEffect(() => {
        const fetchGroups = async () => {
            const response = await fetch("/api/groups")
            const data: IProductGroup[] = await response.json()
            setGroups(data)
        }
        fetchGroups()
    }, [])
    return (
        <div>
            <h1>Groups </h1>
            <ul>
                {groups.map((group) => (
                    <li key={group.id}>{group.name}</li>
                ))}
            </ul>
        </div>
    );
};

export default Groups;