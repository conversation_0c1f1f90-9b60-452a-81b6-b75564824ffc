"use client"
import React, { FC, useEffect } from "react";
import { fetchGroups } from "@/store/groupSlice";
import { useAppDispatch, useAppSelector } from "@/store";
import { Loader2 } from "lucide-react";
import GroupSection from "@/components/dashboard/GroupSection";

/**
 * Home dashboard page displaying product groups and their services.
 * Fetches data from the ServiceAndGroup store.
 * Uses lazy loading for DashboardServiceCard to optimize performance.
 * @component
 */
const Home: FC = () => {
  const dispatch = useAppDispatch();
  const { groups, loading, error } = useAppSelector((state) => state.group);

  useEffect(() => {
    dispatch(fetchGroups());
  }, [dispatch]);


  if (loading) return <div className="flex flex-row items-center justify-center gap-2"><Loader2 className="animate-spin" /> Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {groups.length > 0 && groups.map((group: IProductGroupWithServices, index: number) => (
        <GroupSection
          key={group.id}
          group={group}
          index={index}
          total={groups.length}
        />
      ))}
      {groups.length === 0 && <div className="text-center text-gray-500">No data found, Please click <strong>Sync data from Clinicore</strong> button above to sync.</div>}
    </div>
  );
};

export default Home;