import { getAuth } from '@/lib/auth'
import { headers } from 'next/headers'
import { redirect } from 'next/navigation'
import TopBar from '@/components/TopBar'


export default async function AdminDashboardLayout(props: { children: React.ReactNode }) {
    const session = await (await getAuth()).api.getSession({
        headers: await headers(),
    })

    // Check if user is authenticated
    if (!session) {
        redirect("/login")
    }

    const user = session.user

    // Check if user has admin role
    if (user.role !== 'admin') {
        redirect("/dashboard")
    }

    return (
        <div className="min-h-full">
            <TopBar user={user} />
            <div className="py-10">
                <main>
                    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">{props.children}</div>
                </main>
            </div>
        </div>
    )
}
