# Booking System Implementation Plan

## Executive Summary

This document outlines a comprehensive plan for integrating the existing booking system from the `/old/` directory into the current Next.js application. The plan leverages insights from the legacy codebase while modernizing the architecture to fit the current tech stack.

## Current State Analysis

### Legacy System Architecture
- **Framework**: Next.js with React
- **State Management**: <PERSON><PERSON> (atomic state management)
- **External API**: CliniCore (CC) API integration
- **UI Flow**: Multi-step booking process (Services → Calendar → Form → Success)
- **Authentication**: Custom authentication system

### Current Project Architecture
- **Framework**: Next.js 15.3.5 with React 19
- **Database**: Drizzle ORM with PostgreSQL (Neon)
- **Authentication**: Better Auth system
- **State Management**: Redux Toolkit
- **UI Components**: Radix UI + Tailwind CSS
- **Deployment**: Cloudflare with OpenNext

## Key Components Identified

### Data Models
```typescript
interface Service {
  id: number | string;
  name: string;
  duration: number;
  price: number;
  description?: string;
  groupId?: string;
}

interface Category {
  id: number | string;
  name: string;
  services: Service[];
}

interface Appointment {
  id: string;
  date: string;
  patientId: string;
  serviceIds: number[];
  location?: string;
  status: 'pending' | 'confirmed' | 'cancelled';
}

interface TimeSlot {
  id: string;
  date: string;
  serviceId: number;
  available: boolean;
}
```

### User Flow
1. **Service Selection**: Browse categories and select services
2. **Calendar Selection**: Choose available time slots
3. **Patient Information**: Enter/update contact details
4. **Confirmation**: Review booking details
5. **Success**: Booking confirmation and next steps

## Implementation Strategy

### What to Reuse
- ✅ CliniCore API integration patterns
- ✅ Business logic for service selection and booking
- ✅ Multi-step booking workflow
- ✅ Data models and interfaces
- ✅ Phone number formatting utilities

### What to Modernize
- 🔄 State management (Jotai → Redux Toolkit)
- 🔄 UI components (Custom → Radix UI)
- 🔄 Form handling (Custom → React Hook Form + Zod)
- 🔄 Styling (SCSS → Pure Tailwind)
- 🔄 Calendar component (Custom → react-day-picker)

### What to Rebuild
- 🆕 Authentication integration (Better Auth)
- 🆕 Database layer with local caching
- 🆕 Error handling and loading states
- 🆕 Mobile-responsive design
- 🆕 Admin dashboard for service management

## Proposed Architecture

### Frontend Structure
```
/app/booking/
  page.tsx                 # Main booking entry point
  /steps/
    services/page.tsx      # Service selection
    calendar/page.tsx      # Time slot selection
    details/page.tsx       # Patient information
    confirmation/page.tsx  # Booking review
    success/page.tsx       # Success confirmation

/components/booking/
  BookingWizard.tsx        # Main booking flow controller
  ServiceSelector.tsx      # Service selection component
  CalendarPicker.tsx       # Calendar with slot availability
  PatientForm.tsx          # Patient information form
  BookingConfirmation.tsx  # Booking summary
  BookingProgress.tsx      # Progress indicator

/lib/booking/
  api.ts                   # CliniCore API integration
  types.ts                 # TypeScript definitions
  utils.ts                 # Utility functions
  validation.ts            # Zod schemas
```

### State Management
```typescript
// Redux slices to implement/extend
- bookingSlice: Current booking session state
- appointmentSlice: User's appointments
- serviceSlice: Available services (extend existing)
- groupSlice: Service categories (extend existing)
- slotSlice: Available time slots
- authSlice: User authentication (extend existing)
```

### Database Extensions
```sql
-- New tables to add
CREATE TABLE appointments (
  id VARCHAR(255) PRIMARY KEY,
  patient_id VARCHAR(255) REFERENCES patients(id),
  cc_appointment_id INTEGER UNIQUE,
  service_ids INTEGER[],
  slot_date TIMESTAMP,
  duration INTEGER,
  status VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE time_slots (
  id VARCHAR(255) PRIMARY KEY,
  service_id INTEGER,
  date TIMESTAMP,
  available BOOLEAN DEFAULT true,
  cc_slot_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Implementation Phases

### Phase 1: Foundation Setup (Week 1)
**Duration**: 5-7 days

**Tasks**:
1. **Database Schema Extensions** (1-2 days)
   - Add appointments and time_slots tables
   - Create necessary indexes and relationships
   - Set up migration scripts

2. **Redux Store Extensions** (1-2 days)
   - Create booking, appointment, and slot slices
   - Define action creators and reducers
   - Set up proper TypeScript types

3. **CliniCore API Integration** (2-3 days)
   - Create API service layer
   - Implement authentication and request handling
   - Add error handling and retry logic

**Deliverables**:
- Database migrations ready
- Redux store structure complete
- CliniCore API service functional

### Phase 2: Core Booking Components (Week 2-3)
**Duration**: 10-14 days

**Tasks**:
1. **Service Selection Component** (2-3 days)
   - Category filtering and search
   - Service cards with pricing
   - Multi-service selection

2. **Calendar Component** (3-4 days)
   - Integration with react-day-picker
   - Real-time slot availability
   - Time slot selection UI

3. **Patient Information Form** (2-3 days)
   - React Hook Form integration
   - Zod validation schemas
   - Phone number input with formatting

4. **Booking Confirmation** (2-3 days)
   - Summary of selected services
   - Appointment details review
   - Terms and conditions

**Deliverables**:
- All core booking components functional
- Form validation working
- Component integration complete

### Phase 3: API Integration (Week 3-4)
**Duration**: 7-10 days

**Tasks**:
1. **Services API Endpoints** (3-4 days)
   - Fetch and cache services from CliniCore
   - Sync with local database
   - Category management

2. **Booking API Endpoints** (3-4 days)
   - Create appointments in CliniCore
   - Handle patient creation/updates
   - Appointment status management

3. **Background Sync Jobs** (1-2 days)
   - Periodic data synchronization
   - Conflict resolution
   - Error handling and logging

**Deliverables**:
- All API endpoints functional
- Data synchronization working
- Error handling implemented

### Phase 4: User Experience Polish (Week 4-5)
**Duration**: 7-10 days

**Tasks**:
1. **Booking Wizard** (2-3 days)
   - Step-by-step navigation
   - Progress indicator
   - State persistence

2. **Loading States & Error Handling** (2-3 days)
   - Skeleton components
   - Error boundaries
   - Toast notifications

3. **Mobile Responsiveness** (2-3 days)
   - Mobile-first design
   - Touch-friendly interactions
   - Performance optimization

**Deliverables**:
- Polished user experience
- Mobile-responsive design
- Comprehensive error handling

### Phase 5: Advanced Features (Week 5-6)
**Duration**: 10-14 days

**Tasks**:
1. **User Dashboard** (3-4 days)
   - Appointment history
   - Upcoming appointments
   - Cancellation/rescheduling

2. **Email Notifications** (2-3 days)
   - Booking confirmations
   - Reminder emails
   - Cancellation notifications

3. **Admin Panel** (3-4 days)
   - Service management
   - Appointment overview
   - Settings configuration

4. **Multi-language Support** (2-3 days)
   - German/English translations
   - Locale-specific formatting
   - Currency and date formats

**Deliverables**:
- Complete booking system
- Admin functionality
- Multi-language support

## Dependencies and Prerequisites

### Technical Dependencies
```json
{
  "new-dependencies": {
    "react-hook-form": "^7.x",
    "@hookform/resolvers": "^3.x",
    "moment-timezone": "^0.5.x",
    "react-phone-input-2": "^2.x"
  },
  "existing-dependencies": {
    "zod": "✅ Already installed",
    "sonner": "✅ Already installed",
    "react-day-picker": "✅ Already installed",
    "qs": "✅ Already installed"
  }
}
```

### Environment Variables
```env
# CliniCore API Configuration
CC_API_URL=https://api.clinicore.eu
CC_API_TOKEN=your_api_token
CC_PUBLIC_TOKEN=your_public_token

# Email Service Configuration
EMAIL_SERVICE_API_KEY=your_email_api_key
EMAIL_FROM_ADDRESS=<EMAIL>

# Application Configuration
BOOKING_TIMEZONE=Europe/Berlin
BOOKING_ADVANCE_HOURS=2
BOOKING_WINDOW_DAYS=90
```

### Integration Points

1. **Authentication System**
   - Extend Better Auth for patient profiles
   - Link user accounts with CliniCore patient IDs
   - Handle guest booking flows

2. **Database Integration**
   - Extend existing patients table
   - Implement proper foreign key relationships
   - Set up data sync mechanisms

3. **UI Component Integration**
   - Use existing Radix UI design system
   - Follow current styling patterns
   - Integrate with existing navigation

## Success Criteria

### Functional Requirements
- ✅ Users can browse and select services
- ✅ Real-time slot availability checking
- ✅ Seamless booking process with validation
- ✅ Email confirmations and notifications
- ✅ User dashboard for appointment management
- ✅ Admin panel for service configuration

### Technical Requirements
- ✅ Mobile-responsive design
- ✅ Integration with existing authentication
- ✅ Proper error handling and loading states
- ✅ Performance optimization and caching
- ✅ Accessibility compliance (WCAG 2.1)
- ✅ Multi-language support

### Performance Targets
- Page load time < 2 seconds
- Time to interactive < 3 seconds
- 95%+ uptime
- < 500ms API response times

## Risk Mitigation

### Technical Risks
1. **CliniCore API Limitations**
   - Mitigation: Implement robust caching and fallback mechanisms
   - Contingency: Local appointment management system

2. **Data Synchronization Issues**
   - Mitigation: Implement conflict resolution strategies
   - Contingency: Manual sync tools for administrators

3. **Performance Concerns**
   - Mitigation: Implement proper caching and optimization
   - Contingency: Progressive loading and pagination

### Business Risks
1. **User Experience Complexity**
   - Mitigation: Extensive user testing and feedback loops
   - Contingency: Simplified booking flow options

2. **Integration Downtime**
   - Mitigation: Graceful degradation and offline capabilities
   - Contingency: Manual booking process backup

## Timeline Summary

**Total Estimated Duration**: 6-8 weeks

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| 1 | 1 week | Foundation setup, API integration |
| 2 | 2 weeks | Core booking components |
| 3 | 1.5 weeks | API endpoints and sync |
| 4 | 1.5 weeks | UX polish and mobile |
| 5 | 2 weeks | Advanced features and admin |

## Next Steps

1. **Immediate Actions**
   - Set up development environment with CliniCore credentials
   - Create feature branch for booking system development
   - Begin Phase 1 implementation

2. **Stakeholder Alignment**
   - Review and approve implementation plan
   - Confirm CliniCore API access and limitations
   - Define acceptance criteria for each phase

3. **Resource Allocation**
   - Assign development team members
   - Set up project tracking and communication
   - Schedule regular review meetings

---

*This plan serves as a living document and will be updated as implementation progresses and requirements evolve.*
