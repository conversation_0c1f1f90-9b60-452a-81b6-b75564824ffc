"use client"

import { useEffect, useState } from 'react'
import { authClient } from '@/lib/auth-client'
import { useRouter } from 'next/navigation'

// Use the inferred User type from Better Auth client
type User = typeof authClient.$Infer.Session.user

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

/**
 * Custom hook for authentication state management
 * Provides user data, loading states, and authentication status
 */
export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
  })
  const router = useRouter()

  useEffect(() => {
    let mounted = true

    const checkAuth = async () => {
      try {
        const session = await authClient.getSession()
        
        if (!mounted) return

        if (session?.data?.user) {
          setAuthState({
            user: session.data.user as User,
            isLoading: false,
            isAuthenticated: true,
          })
        } else {
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
          })
        }
      } catch (error) {
        console.error('Auth check failed:', error)
        if (!mounted) return
        
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
        })
      }
    }

    checkAuth()

    return () => {
      mounted = false
    }
  }, [])

  /**
   * Check if user has admin role
   */
  const isAdmin = authState.user?.role === 'admin'

  /**
   * Check if user has specific role
   */
  const hasRole = (role: 'admin' | 'user') => authState.user?.role === role

  /**
   * Redirect to login if not authenticated
   */
  const requireAuth = () => {
    if (!authState.isLoading && !authState.isAuthenticated) {
      router.push('/login')
      return false
    }
    return true
  }

  /**
   * Redirect to dashboard if not admin
   */
  const requireAdmin = () => {
    if (!authState.isLoading) {
      if (!authState.isAuthenticated) {
        router.push('/login')
        return false
      }
      if (!isAdmin) {
        router.push('/dashboard')
        return false
      }
    }
    return true
  }

  /**
   * Sign out user
   */
  const signOut = async () => {
    try {
      await authClient.signOut()
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      })
      router.push('/login')
    } catch (error) {
      console.error('Sign out failed:', error)
    }
  }

  return {
    ...authState,
    isAdmin,
    hasRole,
    requireAuth,
    requireAdmin,
    signOut,
  }
}
