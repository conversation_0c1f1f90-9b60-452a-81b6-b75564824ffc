import type { AppointmentData } from "../store/appointmentBookingSlice"

export const bookAppointment = async (appointmentData: AppointmentData) => {
  const response = await fetch("/api/booking/book", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(appointmentData),
  })

  if (!response.ok) {
    throw new Error("Failed to book appointment")
  }

  return response.json()
}
