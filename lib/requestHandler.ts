import { getAuth } from "./auth"
import { headers } from "next/headers"
import { redirect } from "next/navigation";

/**
 * Represents an authenticated session object returned by Better Auth.
 * @property user - The authenticated user object
 * @property session - The session details (e.g., token)
 */
export interface Session {
    user: {
        id: string;
        email: string;
        name: string;
        image?: string | null;
        emailVerified: boolean;
        createdAt: string | Date;
        updatedAt: string | Date;
    };
    session: {
        token: string;
        [key: string]: unknown;
    };
    [key: string]: unknown;
}

/**
 * Higher-order request handler for Next.js API routes.
 * Checks authentication and either calls the provided method or redirects to login.
 *
 * @param {(req: Request, session: Session) => Response | Promise<Response>} method - The handler to execute if authenticated. Receives the request and session. Can be sync or async.
 * @returns {(req: Request) => Promise<Response>} Wrapped handler with auth check.
 */
export function withAuthHandler(method: (props: { request: Request, session: Session }) => Response | Promise<Response>): (req: Request) => Promise<Response> {
    return async function handler(req: Request): Promise<Response> {
        const auth = await getAuth()
        const session = await auth.api.getSession({
            headers: await headers(),
        })
        if (!session) {
            // Redirect to login page if not authenticated
            return redirect("/login")
        }
        // Call the provided method if authenticated (supports sync or async)
        return await method({ request: req, session })
    }
}