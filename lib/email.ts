import { Resend } from "resend";
import { getEnv } from "@utils/ctx";

/**
 * Email service utility using Resend
 */
class EmailService {
  private resend: Resend | null = null;
  private initialized = false;

  private async initialize() {
    if (this.initialized) return;

    try {
      const env = await getEnv();
      console.log("Initializing email service...");
      console.log("RESEND_API_KEY exists:", !!env.RESEND_API_KEY);

      if (env.RESEND_API_KEY) {
        this.resend = new Resend(env.RESEND_API_KEY);
        this.initialized = true;
        console.log("Email service initialized successfully");
      } else {
        console.warn("RESEND_API_KEY not found in environment variables");
      }
    } catch (error) {
      console.error("Failed to initialize email service:", error);
    }
  }

  /**
   * Send magic link email
   */
  async sendMagicLink(email: string, url: string) {
    console.log(`Attempting to send magic link to ${email}`);
    console.log(`Magic link URL: ${url}`);

    await this.initialize();

    if (!this.resend) {
      console.error("Email service not configured - Resend instance is null");
      console.log(`Magic link for ${email}: ${url}`);
      return { success: false, error: "Email service not configured" };
    }

    try {
      console.log("Sending email via Resend...");
      const emailPayload = {
        from: "Wolanin MD Aesthetics <<EMAIL>>",
        to: email,
        subject: "Anmelden bei Ihrem Konto",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #054224;">Anmelden bei Ihrem Konto</h2>
            <p>Klicken Sie auf den untenstehenden Link, um sich bei Ihrem Konto anzumelden:</p>
            <div style="margin: 30px 0;">
              <a href="${url}" style="background-color: #054224; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Jetzt anmelden
              </a>
            </div>
            <p style="color: #666; font-size: 14px;">
              Dieser Link ist aus Sicherheitsgründen nur für eine begrenzte Zeit gültig.
            </p>
            <p style="color: #666; font-size: 14px;">
              Falls Sie diese E-Mail nicht angefordert haben, können Sie sie ignorieren.
            </p>
          </div>
        `,
      };

      console.log("Email payload:", { ...emailPayload, html: "[HTML_CONTENT]" });

      const { data, error } = await this.resend.emails.send(emailPayload);

      if (error) {
        console.error("Resend API error:", error);
        return { success: false, error: error.message || "Failed to send email" };
      }

      console.log("Email sent successfully:", data);
      return { success: true, data };
    } catch (error: any) {
      console.error("Exception while sending magic link email:", error);
      return { success: false, error: error.message || "Unknown error occurred" };
    }
  }


}

// Export singleton instance
export const emailService = new EmailService();
