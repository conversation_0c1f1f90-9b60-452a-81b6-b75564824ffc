import { createAuthClient } from "better-auth/react";
import { magicLinkClient, inferAdditionalFields } from "better-auth/client/plugins";

/**
 * Better Auth client instance for frontend authentication actions.
 * Includes magic link plugin for passwordless authentication and infers additional fields.
 *
 * @see https://www.better-auth.com/docs/concepts/client
 */
export const authClient = createAuthClient({
    baseURL: typeof window !== 'undefined' ? window.location.origin : undefined,
    plugins: [
        magicLinkClient(),
        inferAdditionalFields({
            user: {
                role: {
                    type: "string"
                }
            }
        })
    ]
});