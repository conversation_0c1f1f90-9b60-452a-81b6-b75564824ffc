{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@utils/*": ["./utils/*"], "@utils": ["./utils"], "@components/*": ["./components/*"], "@database/*": ["./database/*"], "@database": ["./database"]}, "types": ["./cloudflare-env.d.ts", "node", "./types/global.d.ts", "./types/auth.d.ts"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "types/**/*.d.ts"], "exclude": ["node_modules"]}