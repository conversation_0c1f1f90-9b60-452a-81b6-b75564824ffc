# Context Sync

## 2024-07-11

- Added a higher-order request handler (`with<PERSON><PERSON><PERSON><PERSON><PERSON>`) in `lib/requestHandler.ts` that checks authentication and either calls the provided method or redirects to login. Includes JSDoc and cleans up old code. This is part of a plan to standardize protected API route handling.

## 2024-07-11 (cont'd)

- Updated `with<PERSON><PERSON><PERSON><PERSON><PERSON>` in `lib/requestHandler.ts` so the wrapped method now receives both the `Request` and the session object (typed as `any` for now).

## 2024-07-11 (cont'd)

- Refactored `/api/sync` (`app/api/sync/route.ts`) to return grouped online services by group name, matching the required frontend format:
  - Response: `{ status: 200, message: "OK", data: newData }`, where `newData` is an array of groups with their services.
  - Used `Promise.all` for parallel fetching of product groups and online services.
  - Used a `Map` for efficient grouping of services by group name.
  - Transformed service fields as required (e.g., `duration`, `gross`, `externalName`).
  - Cleaned up unused code and removed unnecessary helpers.
  - Added JSDoc to all new/modified functions.
- This was done per user request to match a specific response structure for the booking widget frontend.

## 2024-07-11 (cont'd)

- Updated `/api/sync` to strip HTML tags from `service.description` before returning it in the response.
  - Added a utility function `stripHtml` with JSDoc to sanitize descriptions.
  - Now, only plain text is returned in the `description` field for each service in the response.

## 2024-07-09

- Added Better Auth authentication tables (`users`, `sessions`, `accounts`, `verifications`, `twoFactors`) to `database/schema.ts` using Drizzle ORM's `pgTable`.
- All tables are documented with JSDoc and follow latest best practices for integration with Better Auth.
- Integration plan: Next steps are to install `better-auth` and its Drizzle adapter, initialize Better Auth in the app using the Drizzle adapter and OpenNext's `getEnv()` for all secrets/config, and update API/middleware for session handling.
- Environment variables for auth (e.g., `BETTER_AUTH_SECRET`, OAuth client IDs) will be managed via `.env` or Cloudflare Worker vars and accessed using `getEnv()`.

## 2024-07-09 (cont'd)

- Initialized Better Auth in `lib/auth.ts` using the Drizzle adapter and OpenNext's `getEnv()` for all secrets/config.
- Used `nextCookies` plugin for Next.js integration.
- Exported `auth` as an async function to ensure env and db are loaded at runtime.
- Required env vars: `BETTER_AUTH_SECRET`, `BETTER_AUTH_URL`, and any social provider secrets (e.g., `GOOGLE_CLIENT_ID`). 
- Created Next.js API route handler for Better Auth in `app/api/auth/[...all]/route.ts` using the async `auth` export and `toNextJsHandler`. 

---

# Context Sync: Redux Toolkit Setup for Auth

## 2024-06-11
- **Plan approved:** Initialize Redux Toolkit for login & sign up.
- **Dependencies installed:** @reduxjs/toolkit, react-redux (via pnpm).
- **Redux store created:** `store/store.ts` with JSDoc and type exports.
- **Auth slice created:** `store/authSlice.ts` with login, sign up, logout, error state, and JSDoc.
- **Provider integrated:** App wrapped with Redux Provider in `app/layout.tsx`.
- **Production login integration:**
  - Created `lib/auth-client.ts` with Better Auth client per latest docs.
  - Added `loginUser` async thunk to `authSlice.ts` using `authClient.signIn.email`.
  - Updated `LoginForm` to dispatch thunk, show loading/error state, and use Redux.
  - All new code includes JSDoc.
- **Login/Logout UX:**
  - Login form redirects to `/dashboard` after successful login using `useRouter`.
  - Dashboard sidebar user menu (`NavUser`) now logs out using `authClient.signOut` and redirects to `/login`.
- **TypeScript Safety:**
  - Fixed login thunk result handling in `LoginForm` to be fully type-safe, using `unwrapResult` from Redux Toolkit.
  - No use of `any` or `as any` in the codebase for this flow.
- **Next steps:**
  1. Add sign up flow if needed.
  2. Add session management and logout.
  3. Clean up any old/unused Redux/auth code.

--- 

## 2024-06-10
- Added a new sign up page at `app/signup/page.tsx` duplicating the login page design.
- Created a new `SignUpForm` component to handle user registration, following Redux store and better-auth best practices.
- Will add a new asyncThunk for sign up in `store/authSlice.ts` and ensure all new code is documented with JSDoc.
- Enabled email/password sign up in `lib/auth.ts` by setting `emailAndPassword: { enabled: true }` in the better-auth config.
- Fixed Drizzle Adapter error by exporting `authSchema` from `database/schema.ts` and passing it to the adapter in `lib/auth.ts`. 

## 2024-07-10
- Added `getAuthUser` selector to `store/authSlice.ts`.
  - Purpose: Retrieve the authenticated user from the Redux store (`state.auth.user`).
  - Usage: Import and use `getAuthUser(state)` in components or hooks to access the current user.
  - Includes full JSDoc for type safety and documentation. 

- Updated `AppSidebar` to use the authenticated user from the Redux store via `getAuthUser` selector and `useSelector`.
- Removed hardcoded sample user data from `AppSidebar`. 

## 2024-07-10
- Added `fetchCurrentUser` thunk to `authSlice.ts` to hydrate Redux with the current user session using `authClient.getSession()`.
- Updated `AppSidebar` to dispatch `fetchCurrentUser` on mount if user is null, using typed `useDispatch<AppDispatch>()` and `useEffect`.
- This ensures the Redux user is set after reload and `NavUser` displays for authenticated users.
- `/~` route remains protected server-side via `AuthProvider` in `/~/layout.tsx`. 

## 2024-07-10
- **Dashboard layout refactored:**
  - Removed sidebar and all sidebar-related code from `app/~/layout.tsx`.
  - Added a topbar with 3-column flex layout:
    - **Left:** App name ("Wolanin Asthetic").
    - **Center:** Menu (moved from sidebar, uses `<NavProjects projects={data.projects} />`).
    - **Right:** User card (`<NavUser />` with Redux user) and logout button.
  - Menu logic and sample data moved from `AppSidebar` to layout.
  - User card and logout logic use Redux user and preserve hydration via `fetchCurrentUser` thunk.
  - All unused sidebar code and imports cleaned up.
  - JSDoc added for the layout component. 

- Issue: Global types from types/index.d.ts were not available in the project.
- Cause: tsconfig.json had a 'types' array in compilerOptions, which restricts TypeScript to only load the listed types. The global types file was not included.
- Solution: Added './types/index.d.ts' to the 'types' array in tsconfig.json. Now, both required types and global types are loaded automatically. 

## 2024-07-11 (cont'd)

- Fixed Drizzle ORM initialization in `database/index.ts`:
  - Changed `drizzle({ databaseUrl: env.DATABASE_URL, schema })` to `drizzle(env.DATABASE_URL, { schema })` to match the correct argument order for `drizzle-orm/neon-serverless`.
  - Added JSDoc for `getDB` function.
  - This resolves a TypeScript error about argument types and ensures proper DB connection setup. 

## [2024-07-09] Refactor: /api/sync endpoint no longer uses transactions
- The GET handler in app/api/sync/route.ts was refactored to remove db.transaction usage.
- Product groups are upserted first using upsertGroups, then a mapping from group name to upserted group id is created.
- Services are then upserted using upsertServices, with the correct groupId assigned from the mapping.
- This change was made to avoid using transactions and to ensure clean, sequential upserts. 

- Refactored `DashboardServiceCard` in `components/DashboardServiceCard.tsx`:
  - Only the description area is scrollable and takes all available space between the name and the duration/price.
  - All other info (image, name, duration, price, buttons) remains fixed and always visible. 

## 2024-07-11 (cont'd)

- Optimized `components/DashboardServiceCard.tsx`:
  - Wrapped the component with `React.memo` to prevent unnecessary re-renders when props do not change.
  - Memoized all inline callbacks (move left/right, delete, edit) using `useCallback` for better performance.
  - Updated JSDoc to reflect optimizations.
  - No breaking changes to props or logic; codebase remains clean and type-safe.

# Project & Conversation Context Sync

## Planned Changes (as of [today's date])

1. **API Route**: Add `app/api/groups/route.ts` to fetch product groups and their services from the database, returning them in the required structure for the dashboard.
2. **Store**: Create `store/serviceAndGroupSlice.ts` using Redux Toolkit, with async thunk to fetch from the new API, and state for groups/services. All methods and interfaces will have JSDoc.
3. **Page Refactor**: Update `app/~/page.tsx` to use the new store for fetching and displaying groups/services, removing direct DB access.
4. **Cleanup**: Remove old code, ensure codebase cleanliness, and keep this context file updated.

---

This file will be updated as changes are made to keep project and conversation context in sync. 

## 2024-07-11 (cont'd)

- Split the old `serviceAndGroupSlice.ts` into two separate slices:
  - `serviceSlice.ts` (handles all service-related state, thunks, and reducers)
  - `groupSlice.ts` (handles all group-related state, thunks, and reducers)
- Updated `store.ts` to use `service` and `group` reducers instead of the old combined slice.
- Deleted `serviceAndGroupSlice.ts` to keep the codebase clean.
- All JSDoc and per-action status/error state preserved in the new slices. 

## 2024-07-11 (cont'd)

- Refactored `updateServicePosition` in `app/api/service/route.ts`:
  - Now, when moving a service (A) from position X to Y, it swaps the `displayOrder` with the service (B) currently at Y.
  - If B exists, B is moved to X; A is moved to Y. If no service is at Y, only A is updated.
  - Cleaned up old logic that updated all records with the old position.
  - Added JSDoc for clarity and maintainability.
- This ensures the order is always swapped, matching the required business logic for service reordering. 

## 2024-07-11 (cont'd)

- Added a reusable `useModal` hook and Modal component in `components/Modal.tsx`:
  - `useModal` manages open/close state for a modal and always controls the modal (no internal state in Modal).
  - Each call to `useModal()` returns an independent modal instance, supporting multiple modals per page (e.g., `const a = useModal(); const b = useModal();`).
  - The hook returns `{ open, openModal, closeModal, Modal }`:
    - `open`: boolean, modal open state
    - `openModal()`: function to open the modal
    - `closeModal()`: function to close the modal
    - `Modal`: a component that accepts `title`, `description`, `footer`, and `children` as props
  - Example usage:
    ```tsx
    const modalA = useModal();
    const modalB = useModal();
    <button onClick={modalA.openModal}>Open Modal A</button>
    <modalA.Modal title="A">Body A</modalA.Modal>
    <button onClick={modalB.openModal}>Open Modal B</button>
    <modalB.Modal title="B">Body B</modalB.Modal>
    ```
  - The Modal is a thin wrapper over the shadcn Dialog primitives, with all content and state provided by the hook.
  - JSDoc is included for both the hook and the Modal component.
  - Old code and internal state in Modal have been removed for cleanliness. 

## 2024-07-11 (cont'd)

- Added a reusable `useAlert` hook and Alert component in `components/ui/alert.tsx`:
  - `useAlert` manages open/close state for an alert and always controls the alert (no internal state in Alert).
  - Each call to `useAlert()` returns an independent alert instance, supporting multiple alerts per page (e.g., `const a = useAlert(); const b = useAlert();`).
  - The hook returns `{ open, showAlert, closeAlert, Alert }`:
    - `open`: boolean, alert open state
    - `showAlert(options)`: function to show the alert. If `showConfirm` is true, returns a Promise that resolves to `true` (confirmed) or `false` (cancelled).
    - `closeAlert()`: function to close the alert
    - `Alert`: a component that displays the alert with the current state
  - Example usage:
    ```tsx
    const alert = useAlert();
    const confirmed = await alert.showAlert({
      title: "Delete Item",
      description: "Are you sure you want to delete this item?",
      variant: "destructive",
      confirmLabel: "Delete",
      cancelLabel: "Cancel",
      showConfirm: true
    });
    if (confirmed) {
      // proceed with delete
    }
    <alert.Alert />
    ```
  - The Alert is a thin wrapper over the shadcn Alert primitives, with all content and state provided by the hook.
  - JSDoc is included for both the hook and the Alert component.
  - Old code and internal state in Alert have been removed for cleanliness. 

## 2024-07-11 (cont'd)

- Added a reusable `useConfirm` hook and Confirm dialog component in `components/Confirm.tsx`:
  - `useConfirm` manages open/close state for a confirm dialog and always controls the dialog (no internal state in Confirm).
  - Each call to `useConfirm()` returns an independent confirm instance, supporting multiple confirms per page (e.g., `const a = useConfirm(); const b = useConfirm();`).
  - The hook returns `{ open, confirm, Confirm }`:
    - `open`: boolean, dialog open state
    - `confirm(options)`: function to show the dialog, returns a Promise that resolves to `true` (confirmed) or `false` (cancelled).
    - `Confirm`: a component that displays the dialog with the current state
  - Example usage:
    ```tsx
    const confirm = useConfirm();
    const handleDelete = async () => {
      const confirmed = await confirm.confirm({
        title: "Delete Item",
        description: "Are you sure you want to delete this item?",
        confirmText: "Delete",
        cancelText: "Cancel"
      });
      if (confirmed) {
        // proceed with delete
      }
    };
    <button onClick={handleDelete}>Delete</button>
    <confirm.Confirm />
    ```
  - The Confirm component is a thin wrapper over the shadcn AlertDialog primitives, with all content and state provided by the hook.
  - JSDoc is included for both the hook and the Confirm component.
  - Old code and internal state in Confirm have been removed for cleanliness. 

## 2024-07-11 (cont'd)

- Optimized `app/~/page.tsx` by lazy loading `DashboardServiceCard` using `React.lazy` and `Suspense`.
  - Wrapped service card rendering in a `Suspense` boundary with a spinner fallback for better perceived performance.
  - Cleaned up the old direct import of `DashboardServiceCard`.
  - Updated/added JSDoc for the `Home` component to reflect the optimization.
  - No breaking changes to props or logic; all code remains type-safe and clean. 

## 2024-07-11 (cont'd)

- Refactored `components/Modal.tsx`:
  - Now provides a `ModalProvider` and a `useModal` hook with a `showModal` function, similar to `useConfirm`.
  - `showModal(options)` returns a Promise that resolves to `true` (confirmed) or `false` (cancelled).
  - Modal supports a loading state on the confirm button, which is triggered if an async `onConfirm` is provided.
  - The API is now consistent with `useConfirm`, making it easier to use imperative modals throughout the app.
  - All code is documented and old code is cleaned up. 

## 2024-07-12

- Refactored `app/~/page.tsx` for readability and maintainability:
  - Extracted `ModalInput` to `components/ModalInput.tsx` as a reusable, documented component.
  - Added JSDoc to `IStoreService` and `IProductGroupWithServices` in `types/global.d.ts`.
  - Split group rendering into a new `GroupSection` component within the page file, with full JSDoc.
  - Moved all handler functions (`handleDelete`, `handleEdit`, `altrGroupPosition`) outside the main render logic and documented them.
  - Cleaned up old inline code, removed the inline modal input, and improved logical separation.
  - All new/modified code is documented with JSDoc and old code is removed for codebase cleanliness. 

## 2024-07-12 (cont'd)

- Moved `GroupSection` and `DashboardServiceCard` to `components/dashboard/` for better organization of dashboard-related components.
- Updated all imports in `app/~/page.tsx` and `GroupSection` to use the new paths.
- Removed the inline definition of `GroupSection` from `app/~/page.tsx` and the old import of `DashboardServiceCard`.
- All dashboard components are now modular and reside in a dedicated directory for maintainability. 

## 2024-07-12 (cont'd)

- Refactored `GroupSection` to encapsulate its own delete and edit logic using hooks (`useAppDispatch`, `useConfirm`, `useModal`, `toast`).
- The page now only passes `group`, `index`, `total`, and `onMove` to `GroupSection`.
- This further modularizes dashboard logic and keeps the page component clean. 

## 2024-07-12 (cont'd)

- Moved `altrGroupPosition` logic into `GroupSection`, so it now handles its own move (up/down) logic using hooks and dispatch.
- The page no longer manages group reordering; it only passes `group`, `index`, and `total` to `GroupSection`.
- This completes the encapsulation of all group-specific actions within the component. 

## 2024-07-11 (cont'd)

- Added `ServiceEditModal` in `components/dashboard/ServiceEditModal.tsx`:
  - Modal for editing a service's `displayName`, `displayDescription`, `price`, and `image`.
  - Integrates with Redux store: dispatches `editService` and `uploadServiceImage` thunks.
  - Image upload uses `/api/service/image` endpoint (Cloudflare R2 via worker).
  - All fields are validated and UI shows upload/progress state.
  - JSDoc added for all new methods and interfaces.
- Updated `DashboardServiceCard` to use `ServiceEditModal`:
  - Edit button now opens the modal, passing current service data.
  - On save, updates service in Redux and closes modal.
  - Old edit logic removed; code is clean and type-safe.
- All new logic and modal usage is documented here and in code JSDoc. 

## 2024-07-11 (cont'd)

- Refactored `DashboardServiceCard` to use the `useModal` hook for the edit modal:
  - Removed local state for modal open/close.
  - Now calls `showModal` with `ServiceEditModal` as content, ensuring modal consistency and code cleanliness.
  - All modal logic is now centralized via the Modal context/provider. 

## 2024-07-11 (cont'd)

- Fixed all reported lint errors and warnings across the codebase:
  - **app/api/groups/route.ts**: Removed unused 'desc' import from drizzle-orm.
  - **app/api/service/image/route.ts**: Removed unused 'err' variable in catch block.
  - **app/~/services/page.tsx**: Removed empty interface and unused 'props' parameter; added JSDoc for Services component.
  - **components/Modal.tsx**: Replaced 'any' with 'unknown' in types; removed unused 'e' variable; added JSDoc for ModalProvider and useModal.
  - **components/TopBar.tsx**: Removed unused variables; replaced <img> with Next.js <Image />; escaped unescaped single quotes; added JSDoc for TopBar.
  - **components/dashboard/DashboardServiceCard.tsx**: Removed unused 'currentPosition' prop; added JSDoc for props interface.
  - **components/dashboard/GroupSection.tsx**: Changed 'let' to 'const' for 'initialName'; added JSDoc for GroupSection.
  - **components/dashboard/ServiceEditModal.tsx**: Replaced 'any' with 'unknown' in error handling; added JSDoc for ServiceEditModal.
  - **components/nav-projects.tsx**: Replaced require() imports with ES module imports for lucide-react icons; added JSDoc for NavProjects.
- All code changes include proper cleanup of old code and maintain codebase cleanliness.
- All new or modified methods and interfaces are documented with JSDoc as per project standards.
- This resolves all outstanding lint errors and warnings as of this date. 

## 2024-07-11 (cont'd)

- Removed the 'currentPosition' prop from all usages of <DashboardServiceCard /> in components/dashboard/GroupSection.tsx to fix a type error after the prop was removed from DashboardServiceCardProps. No other references to 'currentPosition' remain. Build and type check should now pass cleanly. 