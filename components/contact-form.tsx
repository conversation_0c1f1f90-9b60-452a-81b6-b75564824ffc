"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useAppDispatch, useAppSelector } from "@/store"
import { nextStep, prevStep, setContact } from "@/store/appointmentBookingSlice"
import { bookAppointment } from "@/lib/api"

export function ContactForm() {
  const dispatch = useAppDispatch()
  const appointmentData = useAppSelector((state) => state.appointmentBooking.appointmentData)
  const [formData, setFormData] = useState(appointmentData.contact)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    const newFormData = { ...formData, [field]: value }
    setFormData(newFormData)
    dispatch(setContact(newFormData))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.firstName && formData.lastName && formData.phone && formData.email) {
      setIsSubmitting(true)
      try {
        await bookAppointment(appointmentData)
        dispatch(nextStep())
      } catch (error) {
        console.error("Failed to book appointment:", error)
        // Handle error (show toast, etc.)
      } finally {
        setIsSubmitting(false)
      }
    }
  }

  const handleBack = () => {
    dispatch(prevStep())
  }

  const isFormValid = formData.firstName && formData.lastName && formData.phone && formData.email

  return (
    <div className="w-full max-w-4xl mx-auto h-full">
      <Card className="w-full h-full flex flex-col">
        <CardHeader>
          <CardTitle>Geben Sie Ihre Daten ein</CardTitle>
        </CardHeader>
        <CardContent className="p-6 flex-1 flex flex-col">
          <form onSubmit={handleSubmit} className="space-y-6 flex-1 flex flex-col">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">
                  Vorname <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="firstName"
                  placeholder="Vorname"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">
                  Nachname <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="lastName"
                  placeholder="Nachname"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">
                  Telefon <span className="text-red-500">*</span>
                </Label>
                <div className="flex">
                  <div className="flex items-center px-3 border border-r-0 rounded-l-md bg-gray-50">
                    <span className="text-sm">🇩🇪</span>
                    <span className="ml-2 text-sm">+880</span>
                  </div>
                  <Input
                    id="phone"
                    className="rounded-l-none"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    required
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">
                  E-Mail <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="EMail"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2 flex-1">
              <Label htmlFor="additionalInfo">Weitere Informationen</Label>
              <Textarea
                id="additionalInfo"
                placeholder="Weitere Informationen"
                rows={4}
                value={formData.additionalInfo}
                onChange={(e) => handleInputChange("additionalInfo", e.target.value)}
                className="resize-none flex-1"
              />
            </div>

            <div className="flex justify-between pt-4">
              <Button type="button" variant="outline" onClick={handleBack}>
                Zurück
              </Button>
              <Button type="submit" disabled={!isFormValid || isSubmitting} className="bg-green-600 hover:bg-green-700">
                {isSubmitting ? "Wird gebucht..." : "Termin vereinbaren"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
