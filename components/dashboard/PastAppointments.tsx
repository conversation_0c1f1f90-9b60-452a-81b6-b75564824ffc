import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Calendar, Clock, MapPin, X, CheckCircle } from 'lucide-react'
import { format, isBefore, parseISO } from 'date-fns'
import { de } from 'date-fns/locale'

interface Appointment {
  id: number
  title: string
  startsAt: string
  endsAt: string
  canceledWhy?: string | null
  services?: Array<{
    id: number
    name: string
  }>
}

interface PastAppointmentsProps {
  appointments: Appointment[]
}

/**
 * Component to display past appointments (completed and cancelled)
 */
export default function PastAppointments({ appointments }: PastAppointmentsProps) {
  // Filter and sort past appointments
  const pastAppointments = appointments
    .filter(appointment => {
      const appointmentDate = parseISO(appointment.startsAt)
      // Include appointments that are in the past OR cancelled (regardless of date)
      return isBefore(appointmentDate, new Date()) || appointment.canceledWhy
    })
    .sort((a, b) => parseISO(b.startsAt).getTime() - parseISO(a.startsAt).getTime()) // Most recent first

  if (pastAppointments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Past Appointments</CardTitle>
          <CardDescription>
            Your appointment history
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              No past appointments found
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const getStatusInfo = (appointment: Appointment) => {
    if (appointment.canceledWhy) {
      return {
        status: 'Cancelled',
        color: 'bg-red-100 text-red-800',
        icon: <X className="h-3 w-3" />
      }
    }
    
    return {
      status: 'Completed',
      color: 'bg-green-100 text-green-800',
      icon: <CheckCircle className="h-3 w-3" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Past Appointments</CardTitle>
        <CardDescription>
          Your appointment history ({pastAppointments.length} appointment{pastAppointments.length !== 1 ? 's' : ''})
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pastAppointments.map((appointment) => {
            const appointmentDate = parseISO(appointment.startsAt)
            const serviceName = appointment.services && appointment.services.length > 0 
              ? appointment.services[0].name 
              : appointment.title || 'Appointment'
            const statusInfo = getStatusInfo(appointment)

            return (
              <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium text-sm">{serviceName}</h4>
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                      {statusInfo.icon}
                      {statusInfo.status}
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {format(appointmentDate, 'EEEE, dd.MM.yyyy', { locale: de })}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {format(appointmentDate, 'HH:mm', { locale: de })} Uhr
                    </span>
                    <span className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      Main Clinic
                    </span>
                  </div>
                  {appointment.canceledWhy && (
                    <div className="mt-2 text-xs text-red-600">
                      Reason: {appointment.canceledWhy}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
