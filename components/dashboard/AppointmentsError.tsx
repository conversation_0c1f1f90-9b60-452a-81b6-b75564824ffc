"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { useState } from 'react'

interface AppointmentsErrorProps {
  error?: string
  onRetry?: () => void
}

/**
 * Error state component for appointments
 */
export default function AppointmentsError({ 
  error = "Failed to load appointments", 
  onRetry 
}: AppointmentsErrorProps) {
  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = async () => {
    if (onRetry) {
      setIsRetrying(true)
      try {
        await onRetry()
      } finally {
        setIsRetrying(false)
      }
    }
  }

  return (
    <Card className="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-800">
          <AlertTriangle className="h-5 w-5" />
          Error Loading Appointments
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-red-700 mb-4">{error}</p>
        {onRetry && (
          <Button 
            onClick={handleRetry} 
            disabled={isRetrying}
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-100"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Retrying...' : 'Try Again'}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
