"use client"

import { useState, useEffect } from 'react'
import AppointmentsList from './AppointmentsList'
import AppointmentsLoading from './AppointmentsLoading'
import AppointmentsError from './AppointmentsError'

interface Appointment {
  id: number
  title: string
  startsAt: string
  endsAt: string
  canceledWhy?: string | null
  services?: Array<{
    id: number
    name: string
  }>
}

interface AppointmentsWrapperProps {
  userEmail?: string
  userPhone?: string
  initialAppointments?: Appointment[]
}

/**
 * Wrapper component that handles appointment data fetching with loading and error states
 */
export default function AppointmentsWrapper({ 
  userEmail, 
  userPhone, 
  initialAppointments = [] 
}: AppointmentsWrapperProps) {
  const [appointments, setAppointments] = useState<Appointment[]>(initialAppointments)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchAppointments = async () => {
    if (!userEmail && !userPhone) {
      setError("User email or phone is required to fetch appointments")
      return
    }

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (userEmail) params.append('email', userEmail)
      if (userPhone) params.append('phone', userPhone)

      const response = await fetch(`/api/booking/appointments?${params}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          setAppointments([])
          return
        }
        throw new Error(`Failed to fetch appointments: ${response.statusText}`)
      }

      const data = await response.json() as { appointments?: any[] }
      setAppointments(data.appointments || [])
    } catch (err) {
      console.error('Error fetching appointments:', err)
      setError(err instanceof Error ? err.message : 'Failed to load appointments')
    } finally {
      setLoading(false)
    }
  }

  const handleRetry = () => {
    fetchAppointments()
  }

  // Only fetch if we don't have initial appointments
  useEffect(() => {
    if (initialAppointments.length === 0) {
      fetchAppointments()
    }
  }, [userEmail, userPhone])

  if (loading) {
    return <AppointmentsLoading />
  }

  if (error) {
    return <AppointmentsError error={error} onRetry={handleRetry} />
  }

  return <AppointmentsList initialAppointments={appointments} />
}
