import React, { Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, EyeOff, <PERSON>cil, ArrowUp, ArrowDown, Loader2 } from "lucide-react";
import { useAppDispatch } from "@/store";
import { deleteGroup, editGroup, alterServiceGroupPosition } from "@/store/groupSlice";
import { useConfirm } from "@/components/Confirm";
import { useModal } from "@/components/Modal";
import { toast } from "sonner";
import { ModalInput } from "@/components/ModalInput";
const DashboardServiceCard = React.lazy(() => import("@/components/dashboard/DashboardServiceCard"));

/**
 * Renders a single group section with its services and actions.
 * Handles its own delete, edit, and move logic using hooks.
 * @param group - The group data to display.
 * @param index - The index of the group in the list.
 * @param total - The total number of groups.
 */
const GroupSection: React.FC<{
    group: IProductGroupWithServices;
    index: number;
    total: number;
}> = ({ group, index, total }) => {
    const dispatch = useAppDispatch();
    const { confirm } = useConfirm();
    const editModal = useModal();

    /**
     * Move the group to a new display order (up or down).
     */
    const altrGroupPosition = (displayOrder: number) => {
        dispatch(alterServiceGroupPosition({ id: group.id, displayOrder }));
    };

    /**
     * Handle hiding or showing a group in the booking widget.
     */
    const handleDelete = async () => {
        const confirmed = await confirm({
            title: `${!group.hidden ? "Hide" : "Show"} ${group.name}`,
            description: (<>
                {!group.hidden && <>Are you sure, You want to hide <strong><code>{group.name}</code></strong> from booking widget? <br /> This action will remove all services from the booking widget.</>}
                {group.hidden && <>Are you sure, You want to show <strong><code>{group.name}</code></strong> in booking widget? <br /> This action will add all services to the booking widget. (As per individual service settings)</>}
            </>),
            confirmText: !group.hidden ? "Hide" : "Show",
            cancelText: "Cancel"
        });
        if (confirmed) {
            dispatch(deleteGroup({ id: group.id })).unwrap().then(() => {
                toast.success(`${!group.hidden ? "Hidden from" : "Shown in"} ${group.name} booking widget!`);
            }).catch(() => {
                toast.error(`${group.name} failed to ${!group.hidden ? "hide from" : "show in"} booking widget!`);
            });
        }
    };

    /**
     * Handle editing a group's display name.
     */
    const handleEdit = async () => {
        const initialName = group.displayName && group.displayName.length > 0 ? group.displayName : group.name;
        let inputValue = initialName;
        const setInputValue = (val: string) => { inputValue = val; };
        await editModal.showModal({
            title: `Edit ${group.name}`,
            description: "Edit the group name",
            content: (
                <ModalInput initialValue={initialName} onChange={setInputValue} />
            ),
            confirmText: "Save",
            onConfirm: async () => {
                await dispatch(editGroup({ id: group.id, displayName: inputValue })).unwrap();
                toast.success(`${group.name} updated!`);
            }
        });
    };

    return (
        <div className="flex flex-col gap-4 py-8 border-b border-gray-200">
            <div className="flex flex-row justify-between items-center">
                <div className="flex flex-row gap-2 items-center">
                    <h2 className="text-xl font-bold">{group.displayName && group.displayName.length > 0 ? group.displayName : group.name}</h2>
                    <Button variant={group.hidden ? "default" : "destructive"} size="icon" onClick={handleDelete}>
                        {group.hidden ? <Eye /> : <EyeOff />}
                    </Button>
                    <Button variant="outline" size="icon" onClick={handleEdit}>
                        <Pencil />
                    </Button>
                </div>
                <div className="flex flex-row gap-2">
                    {index > 0 && <Button variant="outline" size="icon" onClick={() => altrGroupPosition(group.displayOrder - 1)}>
                        <ArrowUp />
                    </Button>}
                    {index < total - 1 && <Button variant="outline" size="icon" onClick={() => altrGroupPosition(group.displayOrder + 1)}>
                        <ArrowDown />
                    </Button>}
                </div>
            </div>
            <div className="grid grid-cols-4 gap-6">
                <Suspense fallback={<div className="col-span-4 flex justify-center items-center"><Loader2 className="animate-spin" /> Loading services...</div>}>
                    {group.services.map((service: IStoreService, serviceIndex: number) => (
                        <DashboardServiceCard key={service.id} service={service} hasPrevious={serviceIndex > 0} hasNext={serviceIndex < group.services.length - 1} />
                    ))}
                </Suspense>
            </div>
        </div>
    );
};

export default GroupSection; 