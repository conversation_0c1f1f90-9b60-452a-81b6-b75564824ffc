"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Calendar, Clock, MapPin, Trash2 } from 'lucide-react'
import { format, isAfter, formatDistanceToNow, parseISO } from 'date-fns'
import { de } from 'date-fns/locale'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "sonner"

interface Appointment {
  id: number
  title: string
  startsAt: string
  endsAt: string
  canceledWhy?: string | null
  services?: Array<{
    id: number
    name: string
  }>
}

interface RecentAppointmentsProps {
  appointments: Appointment[]
}

/**
 * Component to display recent upcoming appointments on the dashboard with cancellation functionality
 */
export default function RecentAppointments({ appointments: initialAppointments }: RecentAppointmentsProps) {
  const [appointments, setAppointments] = useState<Appointment[]>(initialAppointments)
  const [cancellingId, setCancellingId] = useState<number | null>(null)

  const handleCancelAppointment = async (appointmentId: number) => {
    setCancellingId(appointmentId)

    try {
      const response = await fetch(`/api/booking/appointments/${appointmentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: 'cancelled by user' }),
      })

      if (!response.ok) {
        throw new Error('Failed to cancel appointment')
      }

      // Remove the cancelled appointment from the list
      setAppointments(prev => prev.filter(apt => apt.id !== appointmentId))

      toast.success('Appointment cancelled successfully')
    } catch (error) {
      console.error('Error cancelling appointment:', error)
      toast.error('Failed to cancel appointment. Please try again.')
    } finally {
      setCancellingId(null)
    }
  }
  // Filter and sort appointments
  const upcomingAppointments = appointments
    .filter(appointment => {
      // Filter out cancelled appointments
      if (appointment.canceledWhy) return false

      // Filter out past appointments
      const appointmentDate = parseISO(appointment.startsAt)
      return isAfter(appointmentDate, new Date())
    })
    .sort((a, b) => parseISO(a.startsAt).getTime() - parseISO(b.startsAt).getTime())
    .slice(0, 3) // Show only the next 3 appointments

  if (upcomingAppointments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Appointments</CardTitle>
          <CardDescription>
            Your next appointments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              No upcoming appointments
            </p>
            <Button asChild size="sm">
              <Link href="/">
                Book New Appointment
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upcoming Appointments</CardTitle>
        <CardDescription>
          Your next {upcomingAppointments.length} appointment{upcomingAppointments.length !== 1 ? 's' : ''}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {upcomingAppointments.map((appointment) => {
            const appointmentDate = parseISO(appointment.startsAt)
            const serviceName = appointment.services && appointment.services.length > 0
              ? appointment.services[0].name
              : appointment.title || 'Appointment'

            return (
              <div key={appointment.id} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{serviceName}</h4>
                    <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {format(appointmentDate, 'dd.MM.yyyy', { locale: de })}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {format(appointmentDate, 'HH:mm', { locale: de })}
                      </span>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDistanceToNow(appointmentDate, { addSuffix: true, locale: de })}
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex gap-2">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={cancellingId === appointment.id}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        {cancellingId === appointment.id ? 'Cancelling...' : 'Cancel'}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Cancel Appointment</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to cancel your appointment for {serviceName} on{' '}
                          {format(appointmentDate, 'EEEE, dd.MM.yyyy', { locale: de })} at{' '}
                          {format(appointmentDate, 'HH:mm', { locale: de })} Uhr?
                          <br /><br />
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Keep Appointment</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleCancelAppointment(appointment.id)}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Cancel Appointment
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
