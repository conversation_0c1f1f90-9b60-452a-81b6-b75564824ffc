"use client"
import React, { FC, useCallback, useRef } from "react";
import Image from "next/image";
import { ArrowLef<PERSON>, <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Off, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAppDispatch } from "@/store";
import { alterServicePosition, deleteService } from "@/store/servicesSlice";
import { useConfirm } from "@/components/Confirm";
import { toast } from "sonner";
import { useModal } from "@/components/Modal";
import ServiceEditModal from "./ServiceEditModal";

/**
 * Props for DashboardServiceCard component.
 * @typedef {Object} DashboardServiceCardProps
 * @property {IStoreService} service - The service to display
 * @property {boolean} [hasPrevious] - Whether there is a previous service
 * @property {boolean} [hasNext] - Whether there is a next service
 */
interface DashboardServiceCardProps {
    service: IStoreService
    hasPrevious?: boolean
    hasNext?: boolean
}

const DashboardServiceCard: FC<DashboardServiceCardProps> = React.memo(({ service, hasPrevious, hasNext }) => {
    const dispatch = useAppDispatch();
    const { confirm } = useConfirm();
    const { showModal } = useModal();
    const editFormRef = useRef<{ submit: () => void }>(null);

    const handleMoveLeft = useCallback(() => {
        dispatch(alterServicePosition({ displayOrder: service.displayOrder > 1 ? service.displayOrder - 1 : 1, id: service.id }));
    }, [dispatch, service.displayOrder, service.id]);

    const handleMoveRight = useCallback(() => {
        dispatch(alterServicePosition({ displayOrder: service.displayOrder + 1, id: service.id }));
    }, [dispatch, service.displayOrder, service.id]);

    const handleDelete = useCallback(() => {
        confirm({
            title: service.hidden ? "Show Service" : "Hide Service",
            description: service.hidden ? "Are you sure you want to show this service?" : "Are you sure you want to hide this service?",
            confirmText: service.hidden ? "Show" : "Hide",
            cancelText: "Cancel"
        }).then((confirmed) => {
            if (confirmed) {
                dispatch(deleteService({ id: service.id })).unwrap().then(() => {
                    toast.success(service.hidden ? "Service shown!" : "Service hidden!");
                }).catch(() => {
                    toast.error(service.hidden ? "Failed to show service!" : "Failed to hide service!");
                });
            }
        });
    }, [confirm, dispatch, service.hidden, service.id]);

    /**
     * Open the edit modal for this service using the useModal hook.
     */
    const handleEdit = React.useCallback(() => {
        let resolver: ((value: boolean) => void) | null = null;
        const modalPromise = new Promise<boolean>((resolve) => {
            resolver = resolve;
        });
        showModal({
            title: "Edit Service",
            content: (
                <ServiceEditModal
                    ref={editFormRef}
                    service={service}
                    onSubmit={(success) => {
                        if (success && resolver) {
                            resolver(true);
                        }
                    }}
                />
            ),
            cancelText: "Cancel",
            confirmText: "Save",
            onConfirm: () => {
                if (editFormRef.current) {
                    editFormRef.current.submit();
                }
            },
            onCancel: () => { },
        });
        modalPromise.then(() => {
            resolver = null;
        });
    }, [service, showModal]);

    return (
        <>
            <div className="border border-gray-200 rounded-md p-2 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 h-auto flex flex-col">
                <div className="w-full h-48 flex-shrink-0 pb-3">
                    <Image src={service.image || "https://placehold.co/250x250"} alt={service.name} width={250} height={250} className="rounded-md object-cover w-full h-full" unoptimized />
                </div>
                <h3><span className="font-bold">Name: </span>{service.displayName ?? service.name}</h3>
                <p><span className="font-bold">Description: </span></p>
                <span>
                    {service.displayDescription ?? (service.description && service.description.length > 2) ? service.description : (<span className="text-red-500">No description</span>)}
                </span>
                <p><span className="font-bold">Duration: </span>{service.duration} minutes</p>
                <p><span className="font-bold">Price: </span>${service.price}</p>
                <p><span className="font-bold">Current Position in Group: </span>{service.displayOrder}</p>
                <div className="flex flex-row gap-2 justify-between py-2 mt-auto">
                    {hasPrevious && <Button variant="outline" size="icon" onClick={handleMoveLeft}>
                        <ArrowLeft />
                    </Button>}
                    <div className="flex flex-row gap-2">
                        <Button variant="destructive" size="icon" onClick={handleDelete}>
                            {service.hidden ? <Eye /> : <EyeOff />}
                        </Button>
                        <Button variant="outline" size="icon" onClick={handleEdit}>
                            <Pencil />
                        </Button>
                    </div>
                    {hasNext && <Button variant="outline" size="icon" onClick={handleMoveRight}>
                        <ArrowRight />
                    </Button>}
                </div>
            </div>
        </>
    );
});
DashboardServiceCard.displayName = "DashboardServiceCard";
export default DashboardServiceCard;