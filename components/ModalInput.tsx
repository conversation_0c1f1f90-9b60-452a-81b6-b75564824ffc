import React, { useState } from "react";
import { Input } from "@/components/ui/input";

/**
 * ModalInput is a controlled input component for use in modals.
 * @param initialValue - The initial value to display in the input.
 * @param onChange - Callback to notify parent of value changes.
 */
export const ModalInput: React.FC<{
    initialValue: string;
    onChange: (val: string) => void;
}> = ({ initialValue, onChange }) => {
    const [value, setValue] = useState(initialValue);
    return (
        <Input
            type="text"
            value={value}
            onChange={e => {
                setValue(e.target.value);
                onChange(e.target.value);
            }}
        />
    );
}; 