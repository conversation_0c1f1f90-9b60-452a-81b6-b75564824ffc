"use client"

import { cn } from "@/utils/index"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useDispatch, useSelector } from "react-redux"
import { useState, useEffect } from "react"
import { loginUser } from "@/store/authSlice"
import type { RootState, AppDispatch } from "@/store/store"
import { useRouter } from "next/navigation"
import { unwrapResult } from '@reduxjs/toolkit'
import { authClient } from "@/lib/auth-client"
import { Mail, ArrowLeft } from "lucide-react"

/**
 * Login form component with support for both email/password and magic link authentication.
 * Handles multi-step login flow with Better Auth integration.
 */
export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const dispatch = useDispatch<AppDispatch>()
  const { loading, error, user } = useSelector((state: RootState) => state.auth)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [formError, setFormError] = useState<string | null>(null)
  const [magicLinkLoading, setMagicLinkLoading] = useState(false)
  const [magicLinkSent, setMagicLinkSent] = useState(false)
  const [showAuthOptions, setShowAuthOptions] = useState(false)
  const [showPasswordForm, setShowPasswordForm] = useState(false)
  const router = useRouter()

  useEffect(() => {
    /**
     * Redirect to dashboard after successful login.
     */
    if (user) {
      router.push("/~")
    }
  }, [user, router])

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormError(null)

    if (!email.trim()) {
      setFormError("Email is required.")
      return
    }

    if (!validateEmail(email)) {
      setFormError("Please enter a valid email address.")
      return
    }

    setShowAuthOptions(true)
  }

  const handleMagicLink = async () => {
    console.log("handleMagicLink called");
    setFormError(null)
    setMagicLinkLoading(true)
    const root = typeof window !== 'undefined' ? window.location.origin : undefined;

    try {
      console.log("Calling authClient.signIn.magicLink with email:", email);
      const result = await authClient.signIn.magicLink({
        email: email,
        callbackURL: root + "/~"
      })
      console.log("Magic link result:", result);
      setMagicLinkSent(true)
    } catch (err: any) {
      console.error("Magic link error:", err);
      setFormError(err?.message || "Failed to send magic link. Please try again.")
    } finally {
      setMagicLinkLoading(false)
    }
  }

  const handlePasswordLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormError(null)

    if (!password) {
      setFormError("Password is required.")
      return
    }

    try {
      const action = await dispatch(loginUser({ email, password }))
      unwrapResult(action)
    } catch (err) {
      if (typeof err === 'string') {
        setFormError(err)
      } else if (err && typeof err === 'object' && 'message' in err) {
        setFormError(String((err as { message: string }).message))
      } else {
        setFormError("Login failed")
      }
    }
  }

  const resetForm = () => {
    console.log("resetForm called");
    setShowAuthOptions(false)
    setShowPasswordForm(false)
    setMagicLinkSent(false)
    setMagicLinkLoading(false) // Reset loading state
    setFormError(null)
    setPassword("")
  }

  const backToAuthOptions = () => {
    console.log("backToAuthOptions called");
    setShowPasswordForm(false)
    setFormError(null)
    setPassword("")
    // Keep showAuthOptions true to return to auth options
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Welcome back</CardTitle>
          <CardDescription>
            {magicLinkSent
              ? "Check your email for the login link"
              : showAuthOptions
                ? "Choose your login method"
                : "Enter your email to continue"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {magicLinkSent ? (
            // Magic link sent success state
            <div className="grid gap-6 text-center">
              <div className="flex justify-center">
                <div className="bg-green-100 text-green-600 rounded-full p-3">
                  <Mail className="size-6" />
                </div>
              </div>
              <div className="grid gap-3">
                <h3 className="font-semibold">Check your email</h3>
                <p className="text-sm text-muted-foreground">
                  We&apos;ve sent a login link to <strong>{email}</strong>
                </p>
                <p className="text-sm text-muted-foreground">
                  Click the link in the email to sign in to your account.
                </p>
              </div>
              <Button
                variant="outline"
                onClick={resetForm}
                className="w-full"
              >
                <ArrowLeft className="size-4 mr-2" />
                Back to login
              </Button>
            </div>
          ) : showPasswordForm ? (
            // Password form state
            <form onSubmit={handlePasswordLogin}>
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={backToAuthOptions}
                      className="p-1 h-auto"
                    >
                      <ArrowLeft className="size-4" />
                    </Button>
                    <Label htmlFor="password">Password</Label>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    required
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    disabled={loading}
                    autoFocus
                  />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? "Signing in..." : "Sign in"}
                </Button>
                {(formError || error) && (
                  <div className="text-destructive text-sm text-center">
                    {formError || error}
                  </div>
                )}
              </div>
            </form>
          ) : showAuthOptions ? (
            // Auth options state
            <div className="grid gap-6">
              <div className="grid gap-3">
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={resetForm}
                    className="p-1 h-auto"
                  >
                    <ArrowLeft className="size-4" />
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Signing in as <strong>{email}</strong>
                  </span>
                </div>
              </div>
              <div className="grid gap-3">
                <Button
                  onClick={() => {
                    console.log("Magic link button clicked, current loading state:", magicLinkLoading);
                    handleMagicLink();
                  }}
                  disabled={magicLinkLoading}
                  className="w-full"
                  variant="default"
                >
                  <Mail className="size-4 mr-2" />
                  {magicLinkLoading ? "Sending..." : "Send me one time login URL"}
                </Button>
                <Button
                  onClick={() => setShowPasswordForm(true)}
                  variant="outline"
                  className="w-full"
                >
                  Continue with password
                </Button>
              </div>
              {formError && (
                <div className="text-destructive text-sm text-center">
                  {formError}
                </div>
              )}
            </div>
          ) : (
            // Initial email input state
            <form onSubmit={handleEmailSubmit}>
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    disabled={loading}
                    autoFocus
                  />
                </div>
                <Button type="submit" className="w-full">
                  Continue
                </Button>
                {formError && (
                  <div className="text-destructive text-sm text-center">
                    {formError}
                  </div>
                )}
              </div>
            </form>
          )}

          {!magicLinkSent && (
            <div className="text-center text-sm mt-6">
              Don&apos;t have an account?{" "}
              <a href="/signup" className="underline underline-offset-4">
                Sign up
              </a>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
