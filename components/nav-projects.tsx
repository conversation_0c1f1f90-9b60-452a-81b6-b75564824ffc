"use client"

import {
  type LucideIcon,
  Home,
  Fr<PERSON>,
  <PERSON><PERSON><PERSON>
} from "lucide-react"


import Link from "next/link"

// This is sample data for the menu.
const data = {
  projects: [
    {
      name: "Dashboard",
      url: "/~",
      icon: Home,
    },
    {
      name: "Groups",
      url: "/~/groups",
      icon: Frame,
    },
    {
      name: "Services",
      url: "/~/services",
      icon: <PERSON><PERSON><PERSON>,
    },
  ],
};

/**
 * NavProjects component displays a list of project navigation links.
 * @param projects - List of projects
 * @returns JSX.Element
 */
export function NavProjects({
  projects = data.projects,
}: {
  projects?: {
    name: string
    url: string
    icon: LucideIcon
  }[]
}) {

  return (
    <div>
      {projects.map((item) => (
        <Link href={item.url} key={item.name}>
          <item.icon />
          <span>{item.name}</span>
        </Link>
      ))}
    </div>
  )
}
