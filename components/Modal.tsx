"use client"
import React, { useState, useC<PERSON>back, useContext, createContext, ReactNode } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "./ui/button";

type ModalContent = {
    title?: ReactNode;
    description?: ReactNode;
    content?: ReactNode;
    confirmText?: ReactNode;
    cancelText?: ReactNode;
    confirmClassName?: string;
    cancelClassName?: string;
    confirmButtonProps?: Omit<React.ComponentProps<typeof Button>, 'onClick'>;
    cancelButtonProps?: Omit<React.ComponentProps<typeof Button>, 'onClick'>;
    onConfirm?: () => Promise<unknown> | void;
    onCancel?: () => void;
};

type ModalContextType = {
    showModal: (options: Omit<ModalContent, 'resolve'>) => Promise<boolean>;
};

const ModalContext = createContext<ModalContextType | null>(null);

/**
 * Provides modal context and modal dialog functionality to children.
 * @param children React children
 * @returns JSX.Element
 */
export const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [open, setOpen] = useState(false);
    const [content, setContent] = useState<ModalContent | null>(null);
    const [resolver, setResolver] = useState<((value: boolean) => void) | null>(null);
    const [loading, setLoading] = useState(false);

    const showModal = useCallback((options: Omit<ModalContent, 'resolve'>) => {
        setContent(options);
        setOpen(true);
        setLoading(false);
        return new Promise<boolean>((resolve) => {
            setResolver(() => resolve);
        });
    }, []);

    const handleClose = useCallback(() => {
        setOpen(false);
        setLoading(false);
        if (content && content.onCancel) content.onCancel();
        if (resolver) resolver(false);
        setResolver(null);
    }, [resolver, content]);

    const handleConfirm = useCallback(async () => {
        if (content && content.onConfirm) {
            setLoading(true);
            try {
                await content.onConfirm();
                setOpen(false);
                setLoading(false);
                if (resolver) resolver(true);
                setResolver(null);
            } catch {
                setLoading(false);
                // Optionally handle error (e.g., show toast)
            }
        } else {
            setOpen(false);
            setLoading(false);
            if (resolver) resolver(true);
            setResolver(null);
        }
    }, [resolver, content]);

    return (
        <ModalContext.Provider value={{ showModal }}>
            {children}
            <Dialog open={open} onOpenChange={o => { if (!o) handleClose(); }}>
                <DialogContent showCloseButton={true}>
                    <DialogHeader>
                        <DialogTitle>{content?.title}</DialogTitle>
                        <DialogDescription>{content?.description}</DialogDescription>
                    </DialogHeader>
                    {content?.content}
                    <DialogFooter>
                        <Button
                            onClick={handleClose}
                            disabled={loading}
                            className={content?.cancelClassName}
                            {...(content?.cancelButtonProps || {})}
                        >
                            {content?.cancelText || "Cancel"}
                        </Button>
                        <Button
                            onClick={handleConfirm}
                            disabled={loading}
                            className={content?.confirmClassName}
                            {...(content?.confirmButtonProps || {})}
                        >
                            {loading && <span className="animate-spin mr-2">⏳</span>}
                            {content?.confirmText || "Confirm"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </ModalContext.Provider>
    );
};

/**
 * Hook to access modal context.
 * @returns ModalContextType
 */
export const useModal = () => {
    const ctx = useContext(ModalContext);
    if (!ctx) throw new Error("useModal must be used within ModalProvider");
    return ctx;
};