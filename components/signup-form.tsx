"use client"

import { cn } from "@/utils/index"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useDispatch, useSelector } from "react-redux"
import { useState, useEffect } from "react"
import { signUpUser } from "@/store/authSlice"
import type { RootState, AppDispatch } from "@/store/store"
import { useRouter } from "next/navigation"
import { unwrapResult } from '@reduxjs/toolkit'

/**
 * Sign up form component connected to Redux and Better Auth.
 * Handles email/password registration, displays loading/error state, and redirects to dashboard on success.
 *
 * @component
 */
export function SignUpForm({
    className,
    ...props
}: React.ComponentProps<"div">) {
    const dispatch = useDispatch<AppDispatch>()
    const { loading, error, user } = useSelector((state: RootState) => state.auth)
    const [email, setEmail] = useState("<EMAIL>")
    const [password, setPassword] = useState("Abc@123!")
    const [confirmPassword, setConfirmPassword] = useState("Abc@123!")
    const [formError, setFormError] = useState<string | null>(null)
    const router = useRouter()

    useEffect(() => {
        /**
         * Redirect to dashboard after successful sign up.
         */
        if (user) {
            router.push("/~")
        }
    }, [user, router])

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setFormError(null)
        if (!email || !password || !confirmPassword) {
            setFormError("All fields are required.")
            return
        }
        if (password !== confirmPassword) {
            setFormError("Passwords do not match.")
            return
        }
        try {
            const action = await dispatch(signUpUser({ email, password }))
            unwrapResult(action)
        } catch (err) {
            if (typeof err === 'string') {
                setFormError(err)
            } else if (err && typeof err === 'object' && 'message' in err) {
                setFormError(String((err as { message: string }).message))
            } else {
                setFormError("Sign up failed")
            }
        }
    }

    return (
        <div className={cn("flex flex-col gap-6", className)} {...props}>
            <Card>
                <CardHeader className="text-center">
                    <CardTitle className="text-xl">Create your account</CardTitle>
                    <CardDescription>
                        Sign up to get started
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit}>
                        <div className="grid gap-6">
                            <div className="grid gap-6">
                                <div className="grid gap-3">
                                    <Label htmlFor="email">Email</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="<EMAIL>"
                                        required
                                        value={email}
                                        onChange={e => setEmail(e.target.value)}
                                        disabled={loading}
                                    />
                                </div>
                                <div className="grid gap-3">
                                    <Label htmlFor="password">Password</Label>
                                    <Input
                                        id="password"
                                        type="password"
                                        required
                                        value={password}
                                        onChange={e => setPassword(e.target.value)}
                                        disabled={loading}
                                    />
                                </div>
                                <div className="grid gap-3">
                                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                                    <Input
                                        id="confirmPassword"
                                        type="password"
                                        required
                                        value={confirmPassword}
                                        onChange={e => setConfirmPassword(e.target.value)}
                                        disabled={loading}
                                    />
                                </div>
                                <Button type="submit" className="w-full" disabled={loading}>
                                    {loading ? "Signing up..." : "Sign up"}
                                </Button>
                                {(formError || error) && (
                                    <div className="text-destructive text-sm text-center">
                                        {formError || error}
                                    </div>
                                )}
                            </div>
                            <div className="text-center text-sm">
                                Already have an account?{" "}
                                <a href="/login" className="underline underline-offset-4">
                                    Login
                                </a>
                            </div>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    )
} 