"use client"
import React, { FC, ReactNode, useCallback, useContext, useRef, useState } from "react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "./ui/button";

/**
 * Props for the Confirm dialog component.
 */
export type ConfirmProps = {
    /** Dialog title */
    title?: ReactNode;
    /** Dialog description */
    description?: ReactNode;
    /** Confirm button text */
    confirmText?: ReactNode;
    /** Cancel button text */
    cancelText?: ReactNode;
    /** Whether the dialog is open */
    open: boolean;
    /** Called when confirmed */
    onConfirm: () => void;
    /** Called when cancelled or closed */
    onCancel?: () => void;
    /** Props for the confirm button */
    confirmButtonProps?: Omit<React.ComponentProps<typeof Button>, 'onClick'>;
    /** Props for the cancel button */
    cancelButtonProps?: Omit<React.ComponentProps<typeof Button>, 'onClick'>;
};

/**
 * Confirm dialog component using shadcn AlertDialog primitives. Controlled by the ConfirmProvider.
 */
export const Confirm: FC<ConfirmProps> = ({
    title,
    description,
    confirmText = "Confirm",
    cancelText = "Cancel",
    open,
    onConfirm,
    onCancel,
    confirmButtonProps,
    cancelButtonProps,
}) => (
    <AlertDialog open={open} onOpenChange={onCancel}>
        <AlertDialogContent>
            {title && <AlertDialogTitle>{title}</AlertDialogTitle>}
            {description && <AlertDialogDescription>{description}</AlertDialogDescription>}
            <AlertDialogFooter>
                <AlertDialogCancel asChild>
                    <Button variant="outline" color="gray" onClick={onCancel} {...(cancelButtonProps || {})}>
                        {cancelText}
                    </Button>
                </AlertDialogCancel>
                <AlertDialogAction asChild>
                    <Button color="red" onClick={onConfirm} {...(confirmButtonProps || {})}>
                        {confirmText}
                    </Button>
                </AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
);

// Context for confirm dialog
interface ConfirmContextType {
    confirm: (options: {
        title?: ReactNode;
        description?: ReactNode;
        confirmText?: ReactNode;
        cancelText?: ReactNode;
        confirmButtonProps?: React.ComponentProps<typeof Button>;
        cancelButtonProps?: React.ComponentProps<typeof Button>;
    }) => Promise<boolean>;
}
const ConfirmContext = React.createContext<ConfirmContextType | undefined>(undefined);

/**
 * Provider component that renders the Confirm dialog and provides the confirm() function via context.
 * Wrap your app (or page) in this provider to use useConfirm anywhere below.
 */
export const ConfirmProvider: FC<{ children: ReactNode }> = ({ children }) => {
    const [open, setOpen] = useState(false);
    const [state, setState] = useState<{
        title?: ReactNode;
        description?: ReactNode;
        confirmText?: ReactNode;
        cancelText?: ReactNode;
        confirmButtonProps?: React.ComponentProps<typeof Button>;
        cancelButtonProps?: React.ComponentProps<typeof Button>;
    }>({});
    const resolver = useRef<((value: boolean) => void) | null>(null);

    const handleCancel = useCallback(() => {
        setOpen(false);
        if (resolver.current) resolver.current(false);
        resolver.current = null;
    }, []);

    const handleConfirm = useCallback(() => {
        setOpen(false);
        if (resolver.current) resolver.current(true);
        resolver.current = null;
    }, []);

    const confirm = useCallback((options: {
        title?: ReactNode;
        description?: ReactNode;
        confirmText?: ReactNode;
        cancelText?: ReactNode;
        confirmButtonProps?: React.ComponentProps<typeof Button>;
        cancelButtonProps?: React.ComponentProps<typeof Button>;
    }) => {
        setState(options);
        setOpen(true);
        return new Promise<boolean>((resolve) => {
            resolver.current = resolve;
        });
    }, []);

    return (
        <ConfirmContext.Provider value={{ confirm }}>
            {children}
            <Confirm
                {...state}
                open={open}
                onConfirm={handleConfirm}
                onCancel={handleCancel}
            />
        </ConfirmContext.Provider>
    );
};

/**
 * Hook to access the confirm() function from the ConfirmProvider.
 *
 * @returns {object} - { confirm }
 * @example
 * const { confirm } = useConfirm();
 * const confirmed = await confirm({ title: "Delete?", description: "Are you sure?" });
 * if (confirmed) { ... }
 */
export function useConfirm() {
    const ctx = useContext(ConfirmContext);
    if (!ctx) {
        throw new Error("useConfirm must be used within a ConfirmProvider");
    }
    return ctx;
}