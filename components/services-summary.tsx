"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Clock, X } from "lucide-react"
import { useAppDispatch, useAppSelector } from "@/store"
import { nextStep, prevStep, setServices } from "@/store/appointmentBookingSlice"

export function ServicesSummary() {
  const dispatch = useAppDispatch()
  const { services: selectedServices } = useAppSelector((state) => state.appointmentBooking.appointmentData)

  const removeService = (serviceId: string) => {
    const updatedServices = selectedServices.filter((service) => service.id !== serviceId)
    dispatch(setServices(updatedServices))
  }

  const getTotalDuration = () => {
    return selectedServices.reduce((total, service) => total + service.duration, 0)
  }

  const getTotalPrice = () => {
    return selectedServices.reduce((total, service) => total + Number.parseFloat(service.price), 0)
  }

  const handleNext = () => {
    if (selectedServices.length > 0) {
      dispatch(nextStep())
    }
  }

  const handleBack = () => {
    dispatch(prevStep())
  }

  return (
    <div className="w-full max-w-4xl mx-auto h-full">
      <Card className="w-full h-full flex flex-col">
        <CardHeader>
          <CardTitle>Ausgewählte Dienstleistungen</CardTitle>
        </CardHeader>
        <CardContent className="p-6 flex-1 flex flex-col">
          <div className="flex-1 space-y-4 mb-6">
            {selectedServices.map((service) => (
              <div key={service.id} className="border rounded-lg p-4 bg-white">
                <div className="flex items-start gap-4">
                  <img
                    src="/placeholder.svg?height=80&width=80"
                    alt={service.name}
                    className="w-20 h-20 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-base mb-1">{service.name}</h3>
                    {service.externalName && service.externalName !== service.name && (
                      <p className="text-gray-600 text-sm mb-3">{service.externalName}</p>
                    )}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        {service.duration} mins
                      </div>
                      <div className="text-base font-medium">ab € {Number.parseFloat(service.price).toFixed(2)}</div>
                    </div>
                  </div>
                  <button
                    onClick={() => removeService(service.id)}
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X className="w-5 h-5 text-gray-400 hover:text-gray-600" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="border-t pt-6 space-y-4">
            <div className="flex justify-between items-center text-lg">
              <span className="font-medium">Dauer</span>
              <span className="font-medium">{getTotalDuration()} Min.</span>
            </div>
            <div className="flex justify-between items-center text-lg">
              <span className="font-medium">Gesamt</span>
              <span className="font-medium">ab € {getTotalPrice().toFixed(2)}</span>
            </div>
          </div>

          <div className="flex justify-between pt-6">
            <Button variant="outline" onClick={handleBack}>
              Zurück
            </Button>
            <Button
              onClick={handleNext}
              disabled={selectedServices.length === 0}
              className="bg-green-600 hover:bg-green-700"
            >
              Bestätigen
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
