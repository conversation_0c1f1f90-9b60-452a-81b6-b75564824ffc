"use client"

import {
  <PERSON><PERSON>,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"


import { authClient } from "@/lib/auth-client"
import { useRouter } from "next/navigation"
import { Button } from "./ui/button"
import { Loader2, LogOut } from "lucide-react"
import { useState } from "react"

export function NavUser({
  user,
}: {
  user: {
    name: string
    email: string
    avatar: string
  }
}) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  /**
   * Handles user logout using Better Auth and redirects to login page.
   */
  const handleLogout = async () => {
    setIsLoading(true)
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          setIsLoading(false)
          router.push("/login")
        },
      },
    })
  }

  return (
    <div className="flex flex-row gap-2 items-center">
      <Avatar>
        <AvatarImage src={user.avatar} />
        <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
      </Avatar>
      <div className="flex flex-col">
        <p>{user.name}</p>
      </div>
      <Button variant="destructive" size="icon" onClick={handleLogout} className="ml-4" title="Logout" disabled={isLoading}>
        {isLoading ? <Loader2 className="animate-spin" /> : <LogOut />}
      </Button>
    </div>
  )
}
